# NoOver 开发笔记

## 已知问题和解决方案

### 1. 移动端导航问题
**问题：** 在静态导出的APK中，按钮点击无反应，页面无法跳转

**根本原因：** UnifiedLink组件在静态导出时导航逻辑错误

**解决方案：**
- 修改 `components/unified-link.tsx` 第65行
- 将 `window.location.pathname = targetPath` 改为 `window.location.href = targetPath`
- 确保所有路径使用正确的绝对路径格式

**修复内容：**
```typescript
// 修复前
window.location.pathname = targetPath

// 修复后
window.location.href = targetPath
```

**影响范围：**
- 所有移动端页面导航功能
- 添加按钮、排名图表按钮、饼图按钮、设置按钮等

**测试验证：**
- APK构建成功：`app/build/outputs/apk/debug/app-debug.apk`
- 文件大小：5.2MB
- 需要在真机上测试所有导航功能

### 2. 移动端开发环境设置
**Android开发：**
- 使用Android Studio进行开发
- 配置Capacitor环境
- 支持真机调试和模拟器调试

**iOS开发：**
- 使用Xcode进行开发
- 配置Capacitor环境
- 支持真机调试和模拟器调试

### 3. 构建和部署流程
**静态导出：**
```bash
npm run build
npm run export
```

**Android APK构建：**
```bash
cd android
./gradlew assembleDebug
```

**iOS构建：**
```bash
cd ios/App
xcodebuild -workspace App.xcworkspace -scheme App -configuration Debug -destination 'platform=iOS Simulator,name=iPhone 14' build
```

## 开发注意事项

### 路由处理
- 在移动端环境中，必须使用 `window.location.href` 而不是 `window.location.pathname`
- 所有路径都需要正确映射到对应的HTML文件
- 使用绝对路径确保导航正确

### 平台检测
- 使用 `Capacitor.isNativePlatform()` 检测是否在移动端环境中
- 根据平台选择不同的导航策略

### 静态导出限制
- 静态导出的应用无法使用服务器端功能
- 所有数据必须存储在客户端
- 路由处理需要特殊处理

## 常用命令

### 开发环境
```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 静态导出
npm run export
```

### 移动端开发
```bash
# 同步代码到移动端项目
npm run cap:sync

# 在Android Studio中打开
npm run cap:open:android

# 在Xcode中打开
npm run cap:open:ios

# 构建Android APK
cd android && ./gradlew assembleDebug
```

## 文件结构

### 重要文件
- `components/unified-link.tsx` - 统一导航组件
- `capacitor.config.ts` - Capacitor配置文件
- `next.config.mjs` - Next.js配置文件
- `android/` - Android项目目录
- `ios/` - iOS项目目录

### 输出文件
- `out/` - 静态导出目录
- `android/app/build/outputs/apk/debug/` - APK输出目录

## 更新日志

### 2025-08-02
- 修复移动端导航问题
- 更新UnifiedLink组件导航逻辑
- 验证APK构建功能
- **构建成功**: 生成了 5.8MB 的最新APK文件 `NoOver-latest.apk`
- **同步完成**: 成功同步到iOS和Android项目
- **构建状态**: 所有构建问题已解决，应用可正常部署
- **图标更新**: 成功更新Android和iOS应用图标为logo.png
  - Android: 更新所有mipmap目录中的图标文件
  - iOS: 更新主要图标文件
  - 新APK: `NoOver-with-logo.apk` (18MB，包含高清图标)