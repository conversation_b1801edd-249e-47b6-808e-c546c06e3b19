# NoOver 移动端应用

这是 NoOver 固定支出管理应用的移动端版本，使用 Capacitor 技术将 Web 应用转换为原生 Android 和 iOS 应用。

## 🚀 快速开始

### 环境要求

**Android 开发:**
- Node.js 16+ 
- Android Studio
- Android SDK (API 24+)
- Java 17 或 Java 11

**iOS 开发 (仅限 macOS):**
- Xcode 14+
- iOS SDK 13+
- CocoaPods

### 安装依赖

```bash
npm install
```

### 构建移动端应用

使用我们提供的一键构建脚本：

```bash
npm run mobile:build
```

这个脚本会：
1. 构建 Next.js 应用（如果可用）
2. 更新 Capacitor 配置
3. 同步到移动端平台
4. 显示下一步操作指南

## 📱 Android 开发

### 构建 Android 应用

1. **打开 Android Studio:**
   ```bash
   npm run mobile:open
   # 或者
   npx cap open android
   ```

2. **在 Android Studio 中:**
   - 等待 Gradle 同步完成
   - 连接 Android 设备或启动模拟器
   - 点击 Run 按钮构建并安装应用

3. **或者使用命令行直接运行:**
   ```bash
   npm run mobile:android
   # 或者
   npx cap run android
   ```

### 生成 APK 文件

在 Android Studio 中：
1. 选择 `Build` > `Build Bundle(s) / APK(s)` > `Build APK(s)`
2. APK 文件将生成在 `android/app/build/outputs/apk/debug/` 目录中

## 🍎 iOS 开发 (macOS 专用)

### 添加 iOS 平台

```bash
npx cap add ios
```

### 构建 iOS 应用

```bash
npx cap open ios
```

在 Xcode 中构建和运行应用。

## 🛠️ 开发工作流

### 修改代码后同步

当你修改了 Web 代码后，需要重新同步到移动端：

```bash
npm run mobile:sync
# 或者
npx cap sync
```

### 实时开发

对于快速开发，你可以：

1. 启动 Web 开发服务器：
   ```bash
   npm run dev
   ```

2. 在浏览器中测试移动端界面（使用开发者工具的移动端模拟）

3. 定期同步到移动端进行真机测试

## 📁 项目结构

```
subscription-tracker/
├── android/                 # Android 项目文件
├── ios/                     # iOS 项目文件 (如果添加了)
├── public/                  # 静态资源和简化版应用
│   ├── index.html          # 启动页面
│   └── app.html            # 完整应用页面
├── out/                     # Next.js 构建输出
├── capacitor.config.ts      # Capacitor 配置
├── mobile-build.js          # 移动端构建脚本
└── package.json            # 项目配置
```

## 🎨 自定义应用

### 应用图标

替换以下文件中的图标：
- `android/app/src/main/res/mipmap-*/ic_launcher.png`
- `android/app/src/main/res/mipmap-*/ic_launcher_round.png`

### 启动画面

修改：
- `android/app/src/main/res/drawable/splash.png`
- `capacitor.config.ts` 中的 SplashScreen 配置

### 应用名称

在 `capacitor.config.ts` 中修改 `appName`：

```typescript
const config: CapacitorConfig = {
  appId: 'com.noover.app',
  appName: 'NoOver', // 修改这里
  // ...
};
```

## 🔧 故障排除

### Java 版本问题

如果遇到 "Unsupported class file major version" 错误：

1. 检查 Java 版本：`java -version`
2. 确保使用 Java 17 或 Java 11
3. 在 Android Studio 中设置正确的 JDK 版本

### Gradle 同步失败

1. 清理项目：在 Android Studio 中选择 `Build` > `Clean Project`
2. 重新同步：`File` > `Sync Project with Gradle Files`
3. 如果仍有问题，删除 `android/.gradle` 目录后重试

### 设备连接问题

1. 确保启用了 USB 调试
2. 检查设备是否被识别：`adb devices`
3. 尝试重启 ADB：`adb kill-server && adb start-server`

## 📦 发布应用

### Android 发布

1. 在 Android Studio 中生成签名的 APK 或 AAB
2. 上传到 Google Play Console
3. 按照 Google Play 的发布流程

### iOS 发布

1. 在 Xcode 中配置签名证书
2. 构建并上传到 App Store Connect
3. 按照 App Store 的审核流程

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个移动端应用！

## 📄 许可证

与主项目相同的许可证。
