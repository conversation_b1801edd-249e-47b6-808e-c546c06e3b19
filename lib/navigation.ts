import { Capacitor } from '@capacitor/core'

/**
 * 统一的导航工具函数，处理移动端和Web端的导航差异
 */

export function navigateTo(path: string) {
  const fullPath = path.startsWith('/') ? path : `/${path}`
  
  if (Capacitor.isNativePlatform()) {
    // 在移动端环境中，使用简单的相对路径导航
    let targetPath = '';
    
    // 处理特殊路由
    if (fullPath === '/' || fullPath === '') {
      targetPath = 'index.html';
    } else if (fullPath === '/add') {
      targetPath = 'add/index.html';
    } else if (fullPath === '/ranking') {
      targetPath = 'ranking/index.html';
    } else if (fullPath === '/piechart') {
      targetPath = 'piechart/index.html';
    } else if (fullPath.startsWith('/category/')) {
      // 处理分类页面
      const category = fullPath.substring('/category/'.length);
      targetPath = `category/${encodeURIComponent(category)}/index.html`;
    } else {
      // 其他页面，保持原有逻辑
      targetPath = fullPath.substring(1); // 移除开头的斜杠
      
      if (!targetPath.endsWith('.html')) {
        if (!targetPath.endsWith('/')) {
          targetPath = targetPath + '/';
        }
        targetPath = targetPath + 'index.html';
      }
    }
    
    console.log('Navigating to:', targetPath);
    
    // 直接设置路径，不使用完整的URL构建
    window.location.pathname = targetPath;
  } else {
    // 在Web环境中使用 window.location 进行简单导航
    window.location.href = fullPath
  }
}

export function navigateBack() {
  // 统一使用 window.history.back
  window.history.back()
}

export function navigateToHome() {
  navigateTo('/')
}

export function navigateToAdd() {
  navigateTo('/add')
}

export function navigateToPieChart() {
  navigateTo('/piechart')
}

export function navigateToRanking(category?: string) {
  console.log('navigateToRanking 被调用, 分类:', category, '平台:', Capacitor.isNativePlatform() ? '移动端' : 'Web端')
  
  if (category === "全部" || !category) {
    console.log('跳转到全部排名页面')
    navigateTo('/ranking')
  } else {
    // 在移动端，使用 localStorage 传递分类参数
    if (Capacitor.isNativePlatform()) {
      console.log('移动端: 保存分类到 localStorage:', category)
      // 保存选中的分类到 localStorage
      localStorage.setItem('selectedCategory', category)
      navigateTo('/ranking')
    } else {
      console.log('Web端: 使用查询参数:', category)
      // 在 Web 端，使用查询参数
      navigateTo(`/ranking?category=${encodeURIComponent(category)}`)
    }
  }
}