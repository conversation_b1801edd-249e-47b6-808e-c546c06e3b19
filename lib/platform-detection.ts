"use client"

// 平台检测工具
export const isWeb = typeof window !== 'undefined' && 
  (!navigator || navigator.product !== 'ReactNative')

export const isNative = !isWeb

// 动态导入react-native相关模块
export const getReactNative = async () => {
  if (isWeb) {
    return null
  }
  try {
    const { Platform } = await import('react-native')
    return { Platform }
  } catch (error) {
    console.warn('Failed to load react-native:', error)
    return null
  }
}

// 动态导入路由组件
export const getRouterComponents = async () => {
  if (isWeb) {
    const { BrowserRouter } = await import('react-router-dom')
    return { Router: BrowserRouter }
  } else {
    const { NativeRouter } = await import('react-router-native')
    return { Router: NativeRouter }
  }
}