# NoOver 应用图标一键替换脚本

## 功能说明

这个脚本可以一键替换 NoOver 应用的所有图标文件，它会：
- 从 `icons` 目录中复制已存在的图标文件
- 更新 Android 项目图标（所有分辨率）
- 更新 iOS 项目图标（所有尺寸）

## 使用方法

```bash
# 基本用法（使用默认的icons目录）
./replace-icons.sh

# 指定自定义icons目录路径
./replace-icons.sh <icons目录路径>

# 示例
./replace-icons.sh
./replace-icons.sh /path/to/your/icons
```

## 目录结构要求

脚本期望的 icons 目录结构：

```
icons/
├── android/
│   ├── mipmap-mdpi/
│   │   └── ic_launcher.png
│   ├── mipmap-hdpi/
│   │   └── ic_launcher.png
│   ├── mipmap-xhdpi/
│   │   └── ic_launcher.png
│   ├── mipmap-xxhdpi/
│   │   └── ic_launcher.png
│   ├── mipmap-xxxhdpi/
│   │   └── ic_launcher.png
│   └── mipmap-ldpi/
│       └── ic_launcher.png
└── ios/
    └── AppIcon.appiconset/
        ├── <EMAIL>
        ├── <EMAIL>
        ├── <EMAIL>
        ├── <EMAIL>
        ├── <EMAIL>
        ├── <EMAIL>
        ├── <EMAIL>
        ├── <EMAIL>
        ├── <EMAIL>
        ├── <EMAIL>
        ├── <EMAIL>
        ├── <EMAIL>
        ├── <EMAIL>
        ├── <EMAIL>
        ├── <EMAIL>
        └── icon-1024.png
```

## 脚本特性

### 🎯 自动化处理
- **智能调整尺寸**: 自动为不同平台和分辨率调整图标尺寸
- **批量更新**: 一次更新所有相关图标文件
- **错误检查**: 自动检查源文件和目标目录是否存在

### 📱 支持的平台
- **Android**: mdpi, hdpi, xhdpi, xxhdpi, xxxhdpi, ldpi
- **iOS**: 所有标准图标尺寸（20x20 到 1024x1024）
- **Play Store**: 512x512 图标

### 🔧 技术特性
- **跨平台**: 支持 macOS（sips）和 Linux（ImageMagick）
- **容错处理**: 如果没有图片处理工具，会直接复制文件
- **详细日志**: 显示每个文件的更新状态

## 输出说明

脚本会显示以下信息：
- 📱 更新 icons 目录中的文件
- 🤖 更新 Android 项目图标
- 🍎 更新 iOS 项目图标
- ✅ 每个成功更新的文件路径和尺寸

## 更新后的操作

运行脚本后，需要重新构建应用才能看到效果：

### Android
```bash
cd android
./gradlew assembleDebug
```

### iOS
在 Xcode 中重新构建应用

## 注意事项

1. **源文件格式**: 支持 PNG、JPG 等常见图片格式
2. **文件权限**: 确保脚本有执行权限（`chmod +x replace-icons.sh`）
3. **备份建议**: 建议在运行前备份现有的图标文件
4. **构建环境**: 确保已安装 Android Studio 和/或 Xcode

## 故障排除

### 常见错误
- **"源文件不存在"**: 检查文件路径是否正确
- **"权限不足"**: 确保脚本有执行权限
- **"目录不存在"**: 确保在正确的项目目录中运行

### 图片处理工具
- **macOS**: 使用系统自带的 `sips` 命令
- **Linux**: 需要安装 ImageMagick (`sudo apt-get install imagemagick`)

## 文件结构

脚本会更新以下位置的文件：

### Android
```
android/app/src/main/res/mipmap-*/ic_launcher.png
android/app/src/main/res/mipmap-*/ic_launcher_round.png
android/app/src/main/res/mipmap-*/ic_launcher_foreground.png
```

### iOS
```
ios/App/App/Assets.xcassets/AppIcon.appiconset/*.png
```

### Icons 目录
```
icons/android/mipmap-*/ic_launcher.png
icons/ios/AppIcon.appiconset/*.png
icons/android/playstore-icon.png
```

## 版本历史

- **v1.0**: 初始版本，支持基本的图标替换功能