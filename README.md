# NoOver - 固定支出管理应用

一个现代化的固定支出管理应用，帮助用户清晰地了解和管理各种订阅服务和固定支出的经济负担。

## ✨ 功能特性

### 📊 多维度数据展示
- **主页概览**: 显示总支出、负担程度和收入占比
- **饼图分析**: 按分类查看支出分布
- **详细排名**: 查看所有支出项目的详细排名
- **条形图**: 分类内支出项目的对比分析

### 🎯 智能分析
- **负担程度评估**: 根据收入自动计算支出负担等级
- **多时间维度**: 支持日/周/月/年视图切换
- **收入占比**: 实时显示固定支出占收入的百分比
- **工作日计算**: 支持不同工作制度的精确计算

### 🛠️ 便捷管理
- **快速添加**: 预设常见服务，一键添加
- **自定义分类**: 支持创建和管理自定义分类
- **灵活编辑**: 支持编辑和删除支出项目
- **数据持久化**: 本地存储，数据安全可靠

### 📱 用户体验
- **移动端优先**: 响应式设计，完美适配手机
- **统一导航**: 所有页面保持一致的导航体验
- **流畅动画**: 丰富的过渡动画和交互反馈
- **页面滑动**: 支持手势滑动切换时间视图

## 🚀 技术栈

- **框架**: Next.js 15.2.4
- **语言**: TypeScript
- **样式**: Tailwind CSS
- **UI组件**: Radix UI
- **图表**: Recharts
- **图标**: Lucide React
- **状态管理**: React Context + localStorage

## 📦 安装和运行

```bash
# 克隆项目
git clone https://github.com/your-username/subscription-tracker.git

# 进入项目目录
cd subscription-tracker

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm start
```

## 🎨 页面结构

```
├── 主页 (/)                    # 支出概览和统计
├── 饼图分析 (/categories)       # 分类支出分布
├── 详细排名 (/category/全部)    # 所有支出排名
├── 分类详情 (/category/[slug])  # 特定分类的支出详情
└── 添加支出 (/add)             # 添加新的固定支出
```

## 🔧 核心组件

- `AppHeader`: 统一的页面头部导航
- `PieChart`: 支出分类饼图
- `ClickableBarChart`: 可交互的条形图
- `SettingsModal`: 设置模态框
- `ButtonGroup`: 时间视图切换器
- `SwipeHint`: 滑动提示组件

## 📊 数据模型

```typescript
interface Subscription {
  id: string
  name: string
  type: "total" | "monthly" | "yearly"
  price: number
  category?: string
  totalTime?: number
  description?: string
}

interface Settings {
  currency: string
  income: number
  incomeType: "daily" | "monthly" | "yearly"
  workDays: number
}
```

## 🎯 设计理念

这个应用的设计理念是"NoOver"，旨在：

1. **提高财务意识**: 让用户清楚地看到各种固定支出的真实负担
2. **简化管理流程**: 通过直观的界面和便捷的操作降低管理成本
3. **数据驱动决策**: 提供多维度的数据分析帮助用户做出明智的财务决策
4. **警醒消费行为**: 通过负担程度评估提醒用户关注支出合理性

## 📱 移动端支持

应用采用移动端优先的设计，完美支持：
- 触摸操作和手势
- 响应式布局
- PWA 特性
- 可导出为原生 iOS/Android 应用

## 🔮 未来规划

- [ ] 数据导入/导出功能
- [ ] 支出趋势分析
- [ ] 预算设置和提醒
- [ ] 多货币支持
- [ ] 云端数据同步
- [ ] 原生移动应用

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**让每一笔固定支出都变得透明可见** 💡
