"use client"

import React from "react"
import { Home, Plus, Bar<PERSON>hart3, <PERSON><PERSON><PERSON>, Setting<PERSON> } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import MobileLink from "@/components/mobile-link"
import { cn } from "@/lib/utils"

interface BottomNavigationProps {
  currentPage: "home" | "ranking" | "piechart" | "add"
  onSettingsClick?: () => void
}

export default function BottomNavigation({ 
  currentPage, 
  onSettingsClick 
}: BottomNavigationProps) {
  const navItems = [
    {
      id: "home",
      label: "首页",
      icon: Home,
      href: "/",
    },
    {
      id: "ranking",
      label: "排行",
      icon: BarChart3,
      href: "/ranking",
    },
    {
      id: "add",
      label: "新增",
      icon: Plus,
      href: "/add",
      isSpecial: true, // 标记为特殊按钮
    },
    {
      id: "piechart",
      label: "分析",
      icon: PieChart,
      href: "/piechart",
    },
    {
      id: "settings",
      label: "设置",
      icon: Setting<PERSON>,
      onClick: onSettingsClick,
    },
  ]

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 safe-area-bottom-ios">
      <div className="bottom-navigation">
        <div className="flex items-center justify-around px-2 py-2">
          {navItems.map((item) => {
            const isActive = currentPage === item.id
            const Icon = item.icon
            
            if (item.onClick) {
              // 设置按钮
              return (
                <Button
                  key={item.id}
                  variant="ghost"
                  size="sm"
                  onClick={item.onClick}
                  className={cn(
                    "flex flex-col items-center justify-center h-16 w-16 rounded-xl transition-all duration-200",
                    "hover:bg-white/20 hover:scale-105",
                    isActive && "bg-primary/10 text-primary"
                  )}
                >
                  <Icon className="h-5 w-5 mb-1" />
                  <span className="text-xs font-medium">{item.label}</span>
                </Button>
              )
            }

            if (item.isSpecial) {
              // 新增按钮 - 特殊样式
              return (
                <MobileLink key={item.id} href={item.href!}>
                  <Button
                    size="sm"
                    className={cn(
                      "flex flex-col items-center justify-center h-14 w-14 rounded-full transition-all duration-200",
                      "bg-primary text-primary-foreground shadow-lg hover:shadow-xl hover:scale-110",
                      isActive && "bg-primary/90"
                    )}
                  >
                    <Icon className="h-6 w-6" />
                  </Button>
                </MobileLink>
              )
            }

            // 普通导航按钮
            return (
              <MobileLink key={item.id} href={item.href!}>
                <Button
                  variant="ghost"
                  size="sm"
                  className={cn(
                    "flex flex-col items-center justify-center h-16 w-16 rounded-xl transition-all duration-200",
                    "hover:bg-white/20 hover:scale-105",
                    isActive 
                      ? "bg-primary/10 text-primary" 
                      : "text-muted-foreground hover:text-foreground"
                  )}
                >
                  <Icon className="h-5 w-5 mb-1" />
                  <span className="text-xs font-medium">{item.label}</span>
                </Button>
              </MobileLink>
            )
          })}
        </div>
      </div>
    </div>
  )
}
