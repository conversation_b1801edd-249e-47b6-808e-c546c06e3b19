"use client"

import { useEffect } from 'react'
import { Capacitor } from '@capacitor/core'

export default function RSCHandler() {
  useEffect(() => {
    // 优化的 RSC 请求拦截器，处理移动端和静态导出环境
    const originalFetch = window.fetch
    
    window.fetch = async function(input: RequestInfo | URL, init?: RequestInit) {
      const url = typeof input === 'string' ? input : input instanceof URL ? input.href : input.url
      
      // 检查是否是 RSC 相关请求（在静态导出环境中这些请求会失败）
      if (url.includes('_rsc') || url.includes('.txt')) {
        console.log('RSC request intercepted:', url)
        
        // 在移动端环境中，尝试加载对应的HTML页面
        if (Capacitor.isNativePlatform()) {
          // 从URL中提取路径
          let path = url;
          
          // 如果是.txt请求，转换为对应的HTML路径
          if (url.includes('.txt')) {
            path = url.replace('.txt', '');
          }
          
          // 如果是_rsc请求，提取路径部分
          if (url.includes('_rsc')) {
            const urlObj = new URL(url, window.location.origin);
            path = urlObj.pathname.split('_rsc')[0];
          }
          
          // 规范化路径
          if (!path.endsWith('/')) {
            path = path + '/';
          }
          path = path + 'index.html';
          
          try {
            // 尝试加载HTML页面
            const htmlResponse = await originalFetch.call(this, path);
            if (htmlResponse.ok) {
              return htmlResponse;
            }
          } catch (e) {
            console.warn('Failed to load HTML page:', e);
          }
        }
        
        // 如果在移动端加载HTML失败，或者不是移动端环境，返回一个简单的响应
        return new Response('0:', {
          status: 200,
          headers: {
            'Content-Type': 'text/x-component',
          }
        })
      }
      
      // 对于其他请求，正常处理
      try {
        return await originalFetch.call(this, input, init)
      } catch (error) {
        console.warn('Fetch error:', error)
        
        // 如果是网络错误，返回一个模拟的成功响应
        if (error instanceof TypeError && error.message.includes('fetch')) {
          return new Response('', {
            status: 200,
            headers: {
              'Content-Type': 'text/plain',
            }
          })
        }
        
        throw error
      }
    }
    
    // 监听自定义的路由变更事件
    const handleRouteChange = (event: CustomEvent) => {
      const path = event.detail.path;
      console.log('Route changed to:', path);
      
      // 在移动端环境中，尝试直接加载HTML页面
      if (Capacitor.isNativePlatform()) {
        // 规范化路径
        let htmlPath = path;
        if (!htmlPath.endsWith('/')) {
          htmlPath = htmlPath + '/';
        }
        htmlPath = htmlPath + 'index.html';
        
        // 使用setTimeout确保在当前事件循环结束后执行
        setTimeout(() => {
          window.location.replace(htmlPath);
        }, 0);
      }
    };
    
    window.addEventListener('routeChanged', handleRouteChange as EventListener);
    
    return () => {
      // 恢复原始 fetch
      window.fetch = originalFetch;
      window.removeEventListener('routeChanged', handleRouteChange as EventListener);
    }
  }, [])
  
  return null
}
