"use client"

import Link from "next/link"
import { useRouter } from "next/navigation"
import { Capacitor } from '@capacitor/core'

interface UnifiedLinkProps {
  href: string
  children: React.ReactNode
  className?: string
  onClick?: (e: React.MouseEvent) => void
}

export default function UnifiedLink({ href, children, className, onClick }: UnifiedLinkProps) {
  const router = useRouter()

  // 确保 href 有值
  if (!href) {
    console.warn('UnifiedLink: href prop is undefined')
    return <span className={className}>{children}</span>
  }

  const handleClick = (e: React.MouseEvent) => {
    // 阻止默认行为
    e.preventDefault()
    
    // 如果提供了自定义的 onClick 处理器，先调用它
    if (onClick) {
      onClick(e)
    }
    
    const fullPath = href.startsWith('/') ? href : `/${href}`
    
    // 确保路径规范化，移除重复的斜杠
    const normalizedPath = fullPath.replace(/\/+/g, '/')
    
    if (Capacitor.isNativePlatform()) {
      try {
        // 在移动端环境中，直接使用完整的路径
        let targetPath = ''
        
        // 处理特殊路由 - 使用绝对路径
        if (normalizedPath === '/' || normalizedPath === '') {
          targetPath = '/index.html'
        } else if (normalizedPath === '/add') {
          targetPath = '/add/index.html'
        } else if (normalizedPath === '/ranking') {
          targetPath = '/ranking/index.html'
        } else if (normalizedPath === '/piechart') {
          targetPath = '/piechart/index.html'
        } else if (normalizedPath === '/categories') {
          targetPath = '/categories/index.html'
        } else if (normalizedPath.startsWith('/category/')) {
          const category = normalizedPath.substring('/category/'.length)
          targetPath = `/category/${encodeURIComponent(category)}/index.html`
        } else if (normalizedPath.startsWith('/test-category-deletion')) {
          targetPath = '/test-category-deletion/index.html'
        } else {
          // 处理其他路径
          targetPath = normalizedPath + '/index.html'
        }
        
        console.log('Navigating to:', targetPath)
        // 使用完整的 location.href 而不是 pathname
        window.location.href = targetPath
      } catch (error) {
        console.error('Navigation error:', error)
        window.location.href = normalizedPath + '/index.html'
      }
    } else {
      // Web环境 - 直接使用路由器
      router.push(normalizedPath)
    }
  }

  return (
    <Link href={href} className={className} onClick={handleClick}>
      {children}
    </Link>
  )
}