"use client"

import { X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import SubscriptionForm from "@/components/subscription-form"

interface Subscription {
  id: string
  name: string
  type: "total" | "monthly" | "yearly"
  price: number
  totalTime?: number
  category?: string
  startDate?: string
  endDate?: string
}

interface EditSubscriptionModalProps {
  subscription: Subscription | null
  onClose: () => void
  onSave: (subscription: Subscription) => void
}

export default function EditSubscriptionModal({ subscription, onClose, onSave }: EditSubscriptionModalProps) {
  if (!subscription) return null

  const handleSubmit = (data: Omit<Subscription, 'id'>) => {
    const updatedSubscription: Subscription = {
      ...subscription,
      ...data,
    }
    onSave(updatedSubscription)
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50" onClick={onClose}>
      <div className="w-full max-w-4xl max-h-[90vh] overflow-y-auto" onClick={(e) => e.stopPropagation()}>
        <SubscriptionForm
          initialData={subscription}
          onSubmit={handleSubmit}
          submitButtonText="保存修改"
          title="编辑固定支出"
          showQuickSelect={false}
          onCancel={onClose}
        />
      </div>
    </div>
  )
}
