"use client"

import React, { useEffect, useState } from 'react'
import SafeArea from './safe-area'
import { getStatusBarHeight } from './capacitor-initializer'

interface MobileContainerProps {
  children: React.ReactNode
  className?: string
  header?: React.ReactNode
  footer?: React.ReactNode
}

export default function MobileContainer({ 
  children, 
  className = '', 
  header, 
  footer 
}: MobileContainerProps) {
  const [statusBarHeight, setStatusBarHeight] = useState(0)
  
  useEffect(() => {
    // 只在客户端获取状态栏高度
    setStatusBarHeight(getStatusBarHeight())
  }, [])
  
  return (
    <div className={`min-h-screen bg-background gesture-area ${className}`}>
      {/* 顶部安全区域 + 头部 */}
      {header && (
        <div className="sticky top-0 z-40">
          <SafeArea mode="top" statusBarHeight={statusBarHeight}>
            {header}
          </SafeArea>
        </div>
      )}
      
      {/* 主要内容区域 */}
      <div className="flex-1">
        {children}
      </div>
      
      {/* 底部安全区域 + 底部 */}
      {footer && (
        <div className="sticky bottom-0 z-40">
          <SafeArea mode="bottom">
            {footer}
          </SafeArea>
        </div>
      )}
    </div>
  )
}