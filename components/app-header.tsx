"use client"

import React from "react"
import Mobile<PERSON>ink from "@/components/mobile-link"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> as <PERSON><PERSON>hart<PERSON><PERSON> } from "lucide-react"
import HorizontalBar<PERSON>hart from "@/components/icons/horizontal-bar-chart"
import { <PERSON><PERSON> } from "@/components/ui/button"

interface AppHeaderProps {
  currentPage: "home" | "ranking" | "piechart" | "add"
  title: string
  subtitle?: string
  onSettingsClick?: () => void
}

export default function AppHeader({
  currentPage,
  title,
  subtitle,
  onSettingsClick
}: AppHeaderProps) {
  return (
    <div className="glass-header sticky top-0 z-40 safe-area-top py-4 border-b border-white/5">
      <div className="mx-auto px-6 max-w-7xl">
        <div className="space-y-2">
        {/* 第一行：Logo、标题和按钮对齐 */}
        <div className="flex items-center justify-between">
          {/* 左侧：Logo和标题 */}
          <div className="flex items-center space-x-3">
            {/* Logo - 点击回到主页 */}
            <MobileLink href="/">
              <img src="/logo.png" alt="NoOver Logo" className="w-8 h-8 hover:scale-105 transition-all duration-200 cursor-pointer" />
            </MobileLink>

            {/* 标题 */}
            <h1 className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
              {title}
            </h1>
          </div>

          {/* 右侧：功能按钮 */}
          <div className="flex items-center space-x-1">
            {/* 详细排名按钮 */}
            <MobileLink href="/ranking">
              <Button
                variant="ghost"
                size="icon"
                className={`h-10 w-10 rounded-xl hover:scale-105 transition-all duration-200 shadow-sm ${
                  currentPage === "ranking"
                    ? "bg-gradient-to-br from-indigo-500 to-indigo-600 text-white"
                    : "glass-button-white"
                }`}
                disabled={currentPage === "ranking"}
              >
                <HorizontalBarChart className="h-5 w-5" />
              </Button>
            </MobileLink>

            {/* 饼图按钮 */}
            <MobileLink href="/piechart">
              <Button
                variant="ghost"
                size="icon"
                className={`h-10 w-10 rounded-xl hover:scale-105 transition-all duration-200 shadow-sm ${
                  currentPage === "piechart"
                    ? "bg-gradient-to-br from-purple-500 to-purple-600 text-white"
                    : "glass-button-white"
                }`}
                disabled={currentPage === "piechart"}
              >
                <PieChartIcon className="h-5 w-5" />
              </Button>
            </MobileLink>



            {/* 设置按钮 */}
            <Button
              variant="ghost"
              size="icon"
              onClick={onSettingsClick}
              className="h-10 w-10 rounded-xl glass-button-white hover:scale-105 transition-all duration-200 shadow-sm"
            >
              <Settings className="h-5 w-5" />
            </Button>
          </div>
        </div>

        {/* 第二行：页面说明 */}
        {subtitle && (
          <div className="flex justify-start">
            <p className="text-sm text-muted-foreground/80 font-medium">
              {subtitle}
            </p>
          </div>
        )}
      </div>
      </div>
    </div>
  )
}
