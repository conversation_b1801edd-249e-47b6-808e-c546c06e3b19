"use client"

import React from "react"
import MobileLink from "@/components/mobile-link"

interface AppHeaderProps {
  title: string
  subtitle?: string
}

export default function AppHeader({
  title,
  subtitle
}: AppHeaderProps) {
  return (
    <div className="glass-header sticky top-0 z-40 safe-area-top py-4 border-b border-white/5">
      <div className="mx-auto px-6 max-w-7xl">
        <div className="space-y-2">
          {/* Logo和标题 */}
          <div className="flex items-center justify-center">
            <div className="flex items-center space-x-3">
              {/* Logo */}
              <MobileLink href="/">
                <img src="/logo.png" alt="NoOver Logo" className="w-8 h-8 hover:scale-105 transition-all duration-200 cursor-pointer" />
              </MobileLink>

              {/* 标题 */}
              <h1 className="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                {title}
              </h1>
            </div>
          </div>

          {/* 页面说明 */}
          {subtitle && (
            <div className="flex justify-center">
              <p className="text-sm text-muted-foreground/80 font-medium">
                {subtitle}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
