"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { Trash2 } from "lucide-react"

interface SubscriptionCardProps {
  subscription: {
    id: string
    name: string
    type: "total" | "monthly" | "yearly"
    price: number
    category?: string
  }
  dailyCost: number
  displayCost: number
  timeUnit: string
  currencySymbol: string
  onRemove: () => void
  onEdit: () => void
  index: number
}

export default function SubscriptionCard({
  subscription,
  dailyCost,
  displayCost,
  timeUnit,
  currencySymbol,
  onRemove,
  onEdit,
  index,
}: SubscriptionCardProps) {
  const [translateX, setTranslateX] = useState(0)
  const [isDragging, setIsDragging] = useState(false)
  const [startX, setStartX] = useState(0)
  const cardRef = useRef<HTMLDivElement>(null)

  const typeLabels = {
    total: "一次性",
    monthly: "月付",
    yearly: "年付",
  }



  // 左滑手势处理
  const handleTouchStart = (e: React.TouchEvent) => {
    setStartX(e.touches[0].clientX)
    setIsDragging(true)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    if (!isDragging) return

    const currentX = e.touches[0].clientX
    const diffX = startX - currentX

    // 只允许左滑
    if (diffX > 0 && diffX <= 80) {
      setTranslateX(-diffX)
    }
  }

  const handleTouchEnd = () => {
    setIsDragging(false)

    // 如果滑动距离超过40px，显示删除按钮
    if (Math.abs(translateX) > 40) {
      setTranslateX(-80)
    } else {
      setTranslateX(0)
    }
  }

  // 点击其他地方时收起
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (cardRef.current && !cardRef.current.contains(event.target as Node)) {
        setTranslateX(0)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  return (
    <div
      ref={cardRef}
      className="relative overflow-hidden rounded-2xl shadow-lg"
      style={{
        animationDelay: `${index * 0.05}s`,
      }}
    >
      {/* 删除按钮背景 */}
      <div className="absolute right-0 top-0 bottom-0 w-20 bg-destructive/90 backdrop-blur-sm flex items-center justify-center border-l border-white/20">
        <button onClick={onRemove} className="p-3 rounded-full bg-white/20 hover:bg-white/30 transition-colors">
          <Trash2 className="h-5 w-5 text-white" />
        </button>
      </div>

      {/* 主卡片 */}
      <div
        className="glass-card p-3 transition-transform duration-200 ease-out cursor-pointer border border-white/20 hover:border-white/30"
        style={{ transform: `translateX(${translateX}px)` }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        onClick={onEdit}
      >
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            {/* 服务名称和标签 */}
            <div className="flex items-center space-x-3">
              <h3 className="font-medium text-foreground text-base truncate">{subscription.name}</h3>
              {subscription.category && (
                <Badge
                  variant="secondary"
                  className="text-xs px-2 py-0.5 rounded-full shrink-0 bg-blue-500 text-white border-0"
                >
                  {subscription.category}
                </Badge>
              )}
            </div>
          </div>

          {/* 右侧数值 - 一行显示 */}
          <div className="text-right ml-4">
            <div className="text-xl font-light text-foreground flex items-baseline justify-end space-x-1">
              <span>{currencySymbol}{displayCost.toFixed(2)}</span>
              <span className="text-xs text-muted-foreground">/{timeUnit}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
