"use client"

import React, { useEffect, useState } from 'react'
import { Capacitor } from '@capacitor/core'
import { getStatusBarHeight } from './capacitor-initializer'

interface SafeAreaProps {
  children: React.ReactNode
  className?: string
  mode?: 'top' | 'bottom' | 'both' | 'inset'
  statusBarHeight?: number
}

export default function SafeArea({ children, className = '', mode = 'both', statusBarHeight }: SafeAreaProps) {
  const [topPadding, setTopPadding] = useState(0)
  const [bottomPadding, setBottomPadding] = useState(0)

  useEffect(() => {
    const updatePaddings = () => {
      if (typeof window === 'undefined') return
      
      let top = 0
      let bottom = 0

      // 如果是 Capacitor 原生环境，优先使用状态栏高度
      if (Capacitor.isNativePlatform()) {
        top = getStatusBarHeight()
        
        // 对于 Android，状态栏不覆盖内容时不需要额外padding
        if (Capacitor.getPlatform() === 'android') {
          top = 0 // 状态栏不覆盖内容，SafeArea不需要额外padding
        }
        
        // 底部安全区域（Android通常是导航栏高度）
        bottom = Capacitor.getPlatform() === 'android' ? 16 : 20
      } else {
        // Web 环境使用 CSS 环境变量
        try {
          const parsePixels = (value: string) => {
            const match = value.match(/(\d+(?:\.\d+)?)px/)
            return match ? parseFloat(match[1]) : 0
          }
          
          const computedStyle = getComputedStyle(document.documentElement)
          top = parsePixels(computedStyle.getPropertyValue('--sat') || '0px')
          bottom = parsePixels(computedStyle.getPropertyValue('--sab') || '0px')
          
          // 确保 Web 环境也有最小高度
          if (top < 10) top = 10
          if (bottom < 10) bottom = 10
        } catch (error) {
          // 降级到默认值
          top = 10
          bottom = 10
        }
      }

      setTopPadding(top)
      setBottomPadding(bottom)
    }

    // 初始化
    updatePaddings()
    
    // 监听窗口大小变化
    window.addEventListener('resize', updatePaddings)
    
    // 定期更新（针对Android WebView的特殊情况）
    const interval = setInterval(updatePaddings, 2000)
    
    return () => {
      window.removeEventListener('resize', updatePaddings)
      clearInterval(interval)
    }
  }, [])

  const getStyle = () => {
    const style: React.CSSProperties = {}
    
    if (mode === 'top' || mode === 'both' || mode === 'inset') {
      // 优先使用传入的状态栏高度
      style.paddingTop = `${statusBarHeight || topPadding}px`
    }
    
    if (mode === 'bottom' || mode === 'both' || mode === 'inset') {
      style.paddingBottom = `${bottomPadding}px`
    }
    
    return style
  }

  return (
    <div 
      className={className}
      style={getStyle()}
    >
      {children}
    </div>
  )
}