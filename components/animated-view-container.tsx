"use client"

import React, { useState, useEffect, useRef } from "react"
import { type TimeView } from "@/lib/time-config"

interface AnimatedViewContainerProps {
  children: React.ReactNode
  currentTimeView: TimeView
  className?: string
  animationType?: "slide" | "fade" | "scale" | "flip"
}

const timeViewOrder: TimeView[] = ["daily", "weekly", "monthly", "yearly"]

export default function AnimatedViewContainer({
  children,
  currentTimeView,
  className = "",
  animationType = "slide"
}: AnimatedViewContainerProps) {
  const [isAnimating, setIsAnimating] = useState(false)
  const [animationDirection, setAnimationDirection] = useState<"left" | "right">("left")
  const [key, setKey] = useState(0)
  const previousTimeView = useRef<TimeView>(currentTimeView)

  useEffect(() => {
    if (previousTimeView.current !== currentTimeView) {
      const prevIndex = timeViewOrder.indexOf(previousTimeView.current)
      const currentIndex = timeViewOrder.indexOf(currentTimeView)

      // 确定动画方向 - 让卡片动画与按钮移动方向保持一致
      // 右滑（日向年，currentIndex增加）→ 按钮向右移动 → 卡片从左侧滑入 → 使用 "right" 动画
      // 左滑（年向日，currentIndex减少）→ 按钮向左移动 → 卡片从右侧滑入 → 使用 "left" 动画
      const direction = currentIndex > prevIndex ? "right" : "left"
      setAnimationDirection(direction)

      // 开始动画
      setIsAnimating(true)
      setKey(prev => prev + 1)

      // 结束动画
      const timer = setTimeout(() => {
        setIsAnimating(false)
      }, 300)

      previousTimeView.current = currentTimeView

      return () => clearTimeout(timer)
    }
  }, [currentTimeView])

  // 获取动画类名
  const getAnimationClass = () => {
    if (!isAnimating) return ""

    const direction = animationDirection

    switch (animationType) {
      case "slide":
        return direction === "left" ? "animate-slide-in-left" : "animate-slide-in-right"
      case "fade":
        return "animate-fade-in"
      case "scale":
        return "animate-scale-in"
      case "flip":
        return direction === "left" ? "animate-flip-in-left" : "animate-flip-in-right"
      default:
        return ""
    }
  }

  return (
    <div className={`relative ${className}`}>
      <div
        key={key}
        className={`${getAnimationClass()}`}
      >
        {children}
      </div>
    </div>
  )
}
