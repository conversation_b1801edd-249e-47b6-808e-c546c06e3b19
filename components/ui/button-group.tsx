"use client"

import React, { useRef, useEffect, useState } from "react"

interface ButtonGroupOption {
  value: string
  label: string
}

interface ButtonGroupProps {
  options: ButtonGroupOption[]
  value: string
  onChange: (value: string) => void
  className?: string
}

export default function ButtonGroup({ options, value, onChange, className = "" }: ButtonGroupProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [indicatorStyle, setIndicatorStyle] = useState({ left: 0, width: 0 })
  const [isAnimating, setIsAnimating] = useState(false)

  // 更新滑动指示器位置
  useEffect(() => {
    if (!containerRef.current) return

    const activeButton = containerRef.current.querySelector(`[data-value="${value}"]`) as HTMLElement
    if (activeButton) {
      const containerRect = containerRef.current.getBoundingClientRect()
      const buttonRect = activeButton.getBoundingClientRect()

      setIndicatorStyle({
        left: buttonRect.left - containerRect.left,
        width: buttonRect.width
      })
    }
  }, [value])

  const handleOptionClick = (optionValue: string) => {
    if (optionValue === value) return

    setIsAnimating(true)
    onChange(optionValue)

    // 动画完成后重置状态
    setTimeout(() => {
      setIsAnimating(false)
    }, 300)
  }

  return (
    <div className={`relative ${className}`}>
      {/* 背景容器 */}
      <div
        ref={containerRef}
        className="relative flex bg-white/10 backdrop-blur-sm rounded-2xl p-1 border border-white/20 shadow-lg"
      >
        {/* 滑动指示器 */}
        <div
          className="absolute top-1 bottom-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl shadow-lg transition-all duration-300 ease-out"
          style={{
            left: `${indicatorStyle.left}px`,
            width: `${indicatorStyle.width}px`,
            transform: isAnimating ? 'scale(1.05)' : 'scale(1)',
          }}
        >
          {/* 指示器内部光效 */}
          <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent rounded-xl" />
          <div className="absolute inset-0 bg-gradient-to-b from-white/10 to-transparent rounded-xl" />
        </div>

        {/* 按钮选项 */}
        {options.map((option, index) => (
          <button
            key={option.value}
            data-value={option.value}
            onClick={() => handleOptionClick(option.value)}
            className={`relative z-10 px-4 py-2.5 text-sm font-medium transition-all duration-300 rounded-xl flex-1 ${
              value === option.value
                ? "text-white shadow-sm"
                : "text-muted-foreground hover:text-foreground"
            }`}
            style={{
              transform: value === option.value && isAnimating ? 'scale(0.95)' : 'scale(1)',
            }}
          >
            {/* 文字内容 */}
            <span className="relative z-10 transition-all duration-200">
              {option.label}
            </span>

            {/* 悬停效果 */}
            {value !== option.value && (
              <div className="absolute inset-0 bg-white/5 rounded-xl opacity-0 hover:opacity-100 transition-opacity duration-200" />
            )}
          </button>
        ))}
      </div>

      {/* 外部光晕效果 */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-600/10 rounded-2xl blur-xl opacity-50 -z-10" />
    </div>
  )
}