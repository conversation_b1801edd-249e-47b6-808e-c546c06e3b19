"use client"

import { useEffect, useState } from 'react'
import { Capacitor } from '@capacitor/core'
import { SplashScreen } from '@capacitor/splash-screen'
import { StatusBar, Style } from '@capacitor/status-bar'

// 全局状态栏高度状态
let globalStatusBarHeight = 0

export function getStatusBarHeight(): number {
  return globalStatusBarHeight
}

export default function CapacitorInitializer() {
  useEffect(() => {
    const initializeCapacitor = async () => {
      try {
        // 只在 Capacitor 环境中运行
        if (Capacitor.isNativePlatform()) {
          // 隐藏启动画面
          await SplashScreen.hide()
          
          // 设置状态栏 - Android 异形屏适配
          await StatusBar.setStyle({ style: Style.Dark })
          
          // Android 特定配置：设置状态栏不覆盖内容
          if (Capacitor.getPlatform() === 'android') {
            await StatusBar.setOverlaysWebView({ overlay: false })
            await StatusBar.setBackgroundColor({ color: '#ffffff' }) // 白色背景
            
            // 添加 Android CSS 类到 body
            document.body.classList.add('android-webview')
            
            // 获取状态栏高度并存储到全局变量
            try {
              const { height } = await StatusBar.getInfo()
              globalStatusBarHeight = height || 0
              console.log('Android status bar height:', globalStatusBarHeight)
            } catch (error) {
              console.warn('Failed to get status bar height:', error)
              // 使用默认状态栏高度估算值
              globalStatusBarHeight = 24
            }
          }
          
          // 隐藏状态栏（可选）
          // await StatusBar.hide()
        }
      } catch (error) {
        console.warn('Capacitor initialization failed:', error)
        // 即使初始化失败也尝试隐藏启动画面
        try {
          await SplashScreen.hide()
        } catch {
          // 忽略错误
        }
      }
    }

    // 确保在组件挂载后执行
    if (typeof window !== 'undefined') {
      initializeCapacitor()
    }
  }, [])

  return null
}