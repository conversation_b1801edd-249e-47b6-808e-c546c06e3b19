"use client"

import { useState, useEffect } from "react"
import { X, AlertTriangle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import ButtonGroup from "@/components/ui/button-group"

interface SettingsModalProps {
  open: boolean
  onClose: () => void
  workDaysPerMonth: number
  income: number
  incomeType: "yearly" | "monthly" | "daily"
  currency: string
  onSave: (settings: {
    workDays: number
    income: number
    incomeType: "yearly" | "monthly" | "daily"
    currency: string
  }) => void
}

export default function SettingsModal({
  open,
  onClose,
  workDaysPerMonth,
  income,
  incomeType,
  currency,
  onSave,
}: SettingsModalProps) {
  const [workSchedule, setWorkSchedule] = useState<"double-rest" | "big-small-week" | "single-rest" | "custom">("double-rest")
  const [customDays, setCustomDays] = useState(workDaysPerMonth.toString())
  const [incomeAmount, setIncomeAmount] = useState(income.toString())
  const [currentIncomeType, setCurrentIncomeType] = useState(incomeType)
  const [currentCurrency, setCurrentCurrency] = useState(currency)

  // 工作制度选项
  const workScheduleOptions = [
    { value: "double-rest", label: "双休", days: 22 },
    { value: "big-small-week", label: "大小周", days: 24 },
    { value: "single-rest", label: "单休", days: 26 },
    { value: "custom", label: "自定义", days: 0 },
  ]

  // 常用货币单位
  const currencies = [
    { code: "CNY", symbol: "¥", name: "人民币" },
    { code: "USD", symbol: "$", name: "美元" },
    { code: "EUR", symbol: "€", name: "欧元" },
    { code: "GBP", symbol: "£", name: "英镑" },
    { code: "JPY", symbol: "¥", name: "日元" },
    { code: "KRW", symbol: "₩", name: "韩元" },
    { code: "HKD", symbol: "HK$", name: "港币" },
    { code: "TWD", symbol: "NT$", name: "台币" },
  ]

  useEffect(() => {
    // 根据工作天数确定工作制度
    const scheduleOption = workScheduleOptions.find(option => option.days === workDaysPerMonth)
    if (scheduleOption) {
      setWorkSchedule(scheduleOption.value as any)
    } else {
      setWorkSchedule("custom")
      setCustomDays(workDaysPerMonth.toString())
    }

    setIncomeAmount(income.toString())
    setCurrentIncomeType(incomeType)
    setCurrentCurrency(currency)
  }, [open, workDaysPerMonth, income, incomeType, currency])

  if (!open) return null

  const handleSave = () => {
    let workDays: number

    if (workSchedule === "custom") {
      workDays = Number.parseInt(customDays)
      if (workDays <= 0 || workDays > 31) {
        return // 验证失败
      }
    } else {
      const selectedOption = workScheduleOptions.find(option => option.value === workSchedule)
      workDays = selectedOption?.days || 22
    }

    const numIncome = Number.parseFloat(incomeAmount) || 0

    onSave({
      workDays,
      income: numIncome,
      incomeType: currentIncomeType,
      currency: currentCurrency,
    })
    onClose()
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-6 z-50">
      <div className="glass-card rounded-3xl w-full max-w-sm bounce-spring">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <h2 className="text-xl font-medium text-foreground">警醒设置</h2>
          <Button variant="ghost" size="icon" onClick={onClose} className="h-10 w-10">
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6 max-h-[70vh] overflow-y-auto">
          {/* 警醒文案 */}
          <div className="glass-card rounded-2xl p-6 space-y-4">
            <div className="flex items-center space-x-3">
              <AlertTriangle className="h-6 w-6 text-warning" />
              <h3 className="font-medium text-foreground">现代人的数字枷锁</h3>
            </div>
            <p className="text-sm text-muted-foreground leading-relaxed">
              每个固定支出服务都想从你的钱包里分一杯羹，
              <br />
              哪怕只是几块钱、几毛钱，
              <br />
              只要持续不断，它们就心满意足。
            </p>
          </div>

          {/* 工作制度设置项 */}
          <div className="space-y-4">
            <Label className="text-base font-medium text-foreground">
              工作模式选择
            </Label>

            {/* 工作制度按钮组 - 一行显示 */}
            <div className="flex items-center gap-2">
              {workScheduleOptions.slice(0, 3).map((option) => (
                <button
                  key={option.value}
                  onClick={() => setWorkSchedule(option.value as any)}
                  className={`px-2 py-3 rounded-lg text-sm font-medium transition-all duration-300 flex-1 min-w-0 relative overflow-hidden ${
                    workSchedule === option.value
                      ? "bg-blue-500 text-white shadow-lg shadow-blue-500/30 scale-105 border-2 border-blue-400 ring-2 ring-blue-300/50"
                      : "glass-card text-muted-foreground hover:text-foreground hover:bg-white/10 border-2 border-transparent hover:border-white/20"
                  }`}
                >
                  {/* 选中状态的光效背景 */}
                  {workSchedule === option.value && (
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-blue-600/20 animate-pulse" />
                  )}
                  <div className="text-center relative z-10">
                    <div className="font-medium whitespace-nowrap text-xs">{option.label}</div>
                    <div className="text-xs opacity-75 mt-1">{option.days}天</div>
                  </div>
                </button>
              ))}

              {/* 自定义选项 - 与其他按钮样式一致 */}
              <button
                onClick={() => setWorkSchedule("custom")}
                className={`px-2 py-3 rounded-lg text-sm font-medium transition-all duration-300 flex-1 min-w-0 relative overflow-hidden ${
                  workSchedule === "custom"
                    ? "bg-blue-500 text-white shadow-lg shadow-blue-500/30 scale-105 border-2 border-blue-400 ring-2 ring-blue-300/50"
                    : "glass-card text-muted-foreground hover:text-foreground hover:bg-white/10 border-2 border-transparent hover:border-white/20"
                }`}
              >
                {/* 选中状态的光效背景 */}
                {workSchedule === "custom" && (
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-blue-600/20 animate-pulse" />
                )}
                <div className="text-center relative z-10">
                  <div className="font-medium whitespace-nowrap text-xs">自定义</div>
                  <div className="text-xs opacity-75 mt-1">
                    {workSchedule === "custom" ? (
                      <Input
                        type="number"
                        value={customDays}
                        onChange={(e) => setCustomDays(e.target.value)}
                        placeholder="22"
                        min="1"
                        max="31"
                        className="h-4 w-10 text-center bg-transparent border-0 text-xs p-0 text-white placeholder:text-white/60 mx-auto"
                        onClick={(e) => e.stopPropagation()}
                      />
                    ) : (
                      "天数"
                    )}
                  </div>
                </div>
              </button>
            </div>

            <p className="text-xs text-muted-foreground text-center">用于精确计算每日负担成本</p>
          </div>

          {/* 收入设置项 */}
          <div className="space-y-3">
            <Label htmlFor="income" className="text-base font-medium text-foreground">
              个人收入 (可选)
            </Label>

            {/* 收入金额和货币单位 */}
            <div className="flex space-x-2">
              <div className="flex-1">
                <Input
                  id="income"
                  type="number"
                  value={incomeAmount}
                  onChange={(e) => setIncomeAmount(e.target.value)}
                  placeholder="0.00"
                  className="h-12 text-center glass-card border-0 text-lg"
                />
              </div>
              <div className="w-20">
                <select
                  value={currentCurrency}
                  onChange={(e) => setCurrentCurrency(e.target.value)}
                  className="h-12 w-full text-center glass-card border-0 text-lg bg-transparent rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  {currencies.map((curr) => (
                    <option key={curr.code} value={curr.code} className="bg-card text-card-foreground">
                      {curr.symbol}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* 收入周期选择 */}
            <div className="pt-2">
              <ButtonGroup
                options={[
                  { value: "daily", label: "每日" },
                  { value: "monthly", label: "每月" },
                  { value: "yearly", label: "每年" },
                ]}
                value={currentIncomeType}
                onChange={(value) => setCurrentIncomeType(value as any)}
              />
            </div>

            {/* 当前选择的货币说明 */}
            <p className="text-xs text-muted-foreground text-center">
              货币单位：{currencies.find(c => c.code === currentCurrency)?.name} ({currencies.find(c => c.code === currentCurrency)?.symbol})
            </p>
            <p className="text-xs text-muted-foreground text-center">用于计算固定支出占收入比重</p>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-border">
          <Button onClick={handleSave} className="w-full h-12 glass-button rounded-full">
            保存设置
          </Button>
        </div>
      </div>
    </div>
  )
}
