"use client"

import React from "react"
import { Plus } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import MobileLink from "@/components/mobile-link"

interface FloatingAddButtonProps {
  className?: string
}

export default function FloatingAddButton({ className = "" }: FloatingAddButtonProps) {
  return (
    <div className={`fixed bottom-6 right-6 z-50 safe-area-bottom-ios ${className}`}>
      <MobileLink href="/add">
        <Button 
          size="icon" 
          className="h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 glass-button mobile-optimized"
        >
          <Plus className="h-6 w-6" />
        </Button>
      </MobileLink>
    </div>
  )
}
