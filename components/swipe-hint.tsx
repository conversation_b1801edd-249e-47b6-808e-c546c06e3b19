"use client"

import React, { useState, useEffect } from "react"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { type TimeView, timeLabels } from "@/lib/time-config"

interface SwipeHintProps {
  show?: boolean
  currentTimeView?: TimeView
  className?: string
}

const timeViewOrder: TimeView[] = ["daily", "weekly", "monthly", "yearly"]

export default function SwipeHint({
  show = true,
  currentTimeView = "daily",
  className = ""
}: SwipeHintProps) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    if (!show) return

    // 检查是否已经显示过提示
    const hasShownHint = localStorage.getItem("swipe-hint-shown")

    if (!hasShownHint) {
      // 延迟显示提示
      const timer = setTimeout(() => {
        setIsVisible(true)
      }, 1000)

      // 4秒后自动隐藏
      const hideTimer = setTimeout(() => {
        setIsVisible(false)
        localStorage.setItem("swipe-hint-shown", "true")
      }, 5000)

      return () => {
        clearTimeout(timer)
        clearTimeout(hideTimer)
      }
    }
  }, [show])

  if (!isVisible) return null

  const currentIndex = timeViewOrder.indexOf(currentTimeView)
  const canSwipeLeft = currentIndex < timeViewOrder.length - 1
  const canSwipeRight = currentIndex > 0

  const nextTimeView = canSwipeLeft ? timeViewOrder[currentIndex + 1] : null
  const prevTimeView = canSwipeRight ? timeViewOrder[currentIndex - 1] : null

  return (
    <div className={`fixed bottom-20 left-1/2 transform -translate-x-1/2 z-50 ${className}`}>
      <div className="glass-card px-6 py-4 rounded-2xl border border-white/20 shadow-lg animate-bounce">
        <div className="text-center space-y-2">
          <div className="text-sm font-medium text-foreground">
            当前：{timeLabels[currentTimeView]}
          </div>
          <div className="flex items-center justify-center space-x-4 text-xs text-muted-foreground">
            {canSwipeRight && prevTimeView && (
              <div className="flex items-center space-x-1">
                <ChevronLeft className="h-3 w-3 text-blue-500 animate-pulse" />
                <span>右滑到{timeLabels[prevTimeView]}</span>
              </div>
            )}
            {canSwipeLeft && nextTimeView && (
              <div className="flex items-center space-x-1">
                <span>左滑到{timeLabels[nextTimeView]}</span>
                <ChevronRight className="h-3 w-3 text-blue-500 animate-pulse" />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
