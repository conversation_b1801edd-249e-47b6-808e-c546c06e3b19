"use client"

import { useState, useEffect } from "react"
import { DollarSign, Plus, Edit2, Trash2, Check, X, Save } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"

interface Subscription {
  id: string
  name: string
  type: "total" | "monthly" | "yearly"
  price: number
  totalTime?: number
  category?: string
}

interface SubscriptionFormProps {
  initialData?: Subscription | null
  onSubmit: (data: Omit<Subscription, 'id'>) => void
  onCancel?: () => void
  submitButtonText?: string
  title?: string
  showQuickSelect?: boolean
}

const DEFAULT_CATEGORIES = ["娱乐", "音乐", "健康", "工具", "存储", "学习", "生活", "其他"]

// 预设的常见固定支出服务
const PRESET_SUBSCRIPTIONS = [
  { name: "Netflix", price: 68, category: "娱乐", type: "monthly" as const },
  { name: "Spotify", price: 15, category: "音乐", type: "monthly" as const },
  { name: "Adobe Creative Cloud", price: 148, category: "工具", type: "monthly" as const },
  { name: "iCloud 存储", price: 6, category: "存储", type: "monthly" as const },
  { name: "健身房会员", price: 299, category: "健康", type: "monthly" as const },
  { name: "外卖会员", price: 25, category: "生活", type: "monthly" as const },
]

export default function SubscriptionForm({ 
  initialData, 
  onSubmit, 
  onCancel,
  submitButtonText = "保存",
  title = "自定义固定支出",
  showQuickSelect = true
}: SubscriptionFormProps) {
  const [formData, setFormData] = useState({
    name: "",
    type: "monthly" as "total" | "monthly" | "yearly",
    price: "",
    totalTime: "",
    category: "其他",
  })

  const [categories, setCategories] = useState<string[]>(DEFAULT_CATEGORIES)
  const [newCategoryName, setNewCategoryName] = useState("")
  const [editingCategory, setEditingCategory] = useState<{ index: number; name: string; originalName: string } | null>(null)
  const [deleteMode, setDeleteMode] = useState(false)
  const [clickTimer, setClickTimer] = useState<NodeJS.Timeout | null>(null)
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null)
  const [currency, setCurrency] = useState("CNY")

  // 货币符号映射
  const getCurrencySymbol = (currencyCode: string) => {
    const currencyMap: { [key: string]: string } = {
      CNY: "¥", USD: "$", EUR: "€", GBP: "£", JPY: "¥", KRW: "₩", HKD: "HK$", TWD: "NT$",
    }
    return currencyMap[currencyCode] || "¥"
  }

  // 初始化数据
  useEffect(() => {
    // 加载分类
    const savedCategories = localStorage.getItem("categories")
    let loadedCategories = DEFAULT_CATEGORIES
    if (savedCategories) {
      loadedCategories = JSON.parse(savedCategories)
    }
    setCategories(loadedCategories)

    if (initialData) {
      // 验证初始数据的分类是否存在
      const validCategory = loadedCategories.includes(initialData.category || "")
        ? initialData.category
        : (loadedCategories.includes("其他") ? "其他" : loadedCategories[0])

      setFormData({
        name: initialData.name,
        type: initialData.type,
        price: initialData.price.toString(),
        totalTime: initialData.totalTime?.toString() || "",
        category: validCategory || "其他",
      })
    }

    // 加载货币设置
    const savedSettings = localStorage.getItem("settings")
    if (savedSettings) {
      const { currency } = JSON.parse(savedSettings)
      setCurrency(currency || "CNY")
    }
  }, [initialData])

  // 监听分类变化，确保当前选中的分类始终有效
  useEffect(() => {
    if (categories.length > 0 && !categories.includes(formData.category)) {
      // 如果当前选中的分类不在可用分类列表中，自动切换到安全的分类
      const fallbackCategory = categories.includes("其他") ? "其他" : categories[0]
      setFormData(prev => ({ ...prev, category: fallbackCategory }))
    }
  }, [categories, formData.category])

  const updateFormData = (key: string, value: string) => {
    setFormData(prev => {
      const newData = { ...prev, [key]: value }

      // 当选择"一次性"时，如果时长为空，设置默认值为 12
      if (key === "type" && value === "total" && !prev.totalTime) {
        newData.totalTime = "12"
      }

      return newData
    })
  }

  const selectPreset = (preset: typeof PRESET_SUBSCRIPTIONS[0]) => {
    setFormData({
      name: preset.name,
      type: preset.type,
      price: preset.price.toString(),
      totalTime: "",
      category: preset.category,
    })
  }

  // 分类管理函数
  const saveCategories = (newCategories: string[]) => {
    setCategories(newCategories)
    localStorage.setItem("categories", JSON.stringify(newCategories))
  }

  // 检查分类是否被支出使用
  const isCategoryInUse = (categoryName: string): boolean => {
    const savedSubscriptions = localStorage.getItem("subscriptions")
    if (!savedSubscriptions) return false

    const subscriptions = JSON.parse(savedSubscriptions)
    return subscriptions.some((sub: any) => (sub.category || "其他") === categoryName)
  }

  const addCategory = () => {
    if (newCategoryName.trim() && !categories.includes(newCategoryName.trim())) {
      const newCategories = [...categories, newCategoryName.trim()]
      saveCategories(newCategories)
      updateFormData("category", newCategoryName.trim())
    }
    setNewCategoryName("")
  }

  // 进入删除模式
  const enterDeleteMode = () => {
    setDeleteMode(true)
  }

  // 退出删除模式
  const exitDeleteMode = () => {
    setDeleteMode(false)
  }

  // 删除分类
  const deleteCategory = (categoryToDelete: string) => {
    if (categories.length <= 1) return

    // 检查分类是否被支出使用
    if (isCategoryInUse(categoryToDelete)) {
      // 如果分类正在被使用，不允许删除
      // 可以在这里添加提示信息，但为了保持简洁，我们静默阻止删除
      return
    }

    const newCategories = categories.filter(cat => cat !== categoryToDelete)
    saveCategories(newCategories)

    // 如果删除的是当前选中的分类，需要选择一个新的分类
    if (formData.category === categoryToDelete) {
      // 优先选择"其他"分类，如果不存在则选择第一个可用分类
      const fallbackCategory = newCategories.includes("其他") ? "其他" : newCategories[0]
      updateFormData("category", fallbackCategory)
    }
  }

  // 处理单击和双击
  const handleCategoryClick = (category: string, index: number) => {
    if (deleteMode) return
    
    if (clickTimer) {
      // 双击 - 编辑分类
      clearTimeout(clickTimer)
      setClickTimer(null)
      startEditCategory(index)
    } else {
      // 单击 - 延迟执行选择分类
      const timer = setTimeout(() => {
        updateFormData("category", category)
        setClickTimer(null)
      }, 200)
      setClickTimer(timer)
    }
  }

  // 长按进入删除模式
  const handleMouseDown = (index: number) => {
    if (!deleteMode) {
      const timer = setTimeout(() => {
        enterDeleteMode()
      }, 600)
      setLongPressTimer(timer)
    }
  }

  const handleMouseUp = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer)
      setLongPressTimer(null)
    }
  }

  const handleMouseLeave = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer)
      setLongPressTimer(null)
    }
  }

  // 开始编辑分类
  const startEditCategory = (index: number) => {
    const category = categories[index]
    setEditingCategory({ index, name: category, originalName: category })
  }

  // 更新编辑中的分类名称
  const updateEditingName = (name: string) => {
    if (editingCategory) {
      setEditingCategory({ ...editingCategory, name })
    }
  }

  // 确认编辑
  const confirmEdit = () => {
    if (!editingCategory || !editingCategory.name.trim()) return

    const newName = editingCategory.name.trim()
    if (newName === editingCategory.originalName) {
      setEditingCategory(null)
      return
    }

    if (categories.includes(newName)) {
      return
    }

    const newCategories = [...categories]
    newCategories[editingCategory.index] = newName
    saveCategories(newCategories)

    if (formData.category === editingCategory.originalName) {
      updateFormData("category", newName)
    }

    setEditingCategory(null)
  }

  // 删除正在编辑的分类
  const deleteEditingCategory = () => {
    if (!editingCategory) return

    const categoryToDelete = editingCategory.originalName

    // 检查分类是否被支出使用
    if (isCategoryInUse(categoryToDelete)) {
      // 如果分类正在被使用，不允许删除
      return
    }

    const newCategories = categories.filter((_, i) => i !== editingCategory.index)
    saveCategories(newCategories)

    // 如果删除的是当前选中的分类，需要选择一个新的分类
    if (formData.category === categoryToDelete) {
      // 优先选择"其他"分类，如果不存在则选择第一个可用分类
      const fallbackCategory = newCategories.includes("其他") ? "其他" : newCategories[0]
      updateFormData("category", fallbackCategory)
    }

    setEditingCategory(null)
  }

  // 取消编辑
  const cancelEdit = () => {
    setEditingCategory(null)
  }

  const handleSubmit = () => {
    if (!formData.name.trim() || !formData.price || Number.parseFloat(formData.price) <= 0) {
      return
    }

    if (formData.type === "total" && !formData.totalTime) {
      return
    }

    // 验证选中的分类是否仍然存在
    let validCategory = formData.category
    if (!categories.includes(formData.category)) {
      // 如果选中的分类不存在，自动选择第一个可用分类
      validCategory = categories.length > 0 ? categories[0] : "其他"
      // 同步更新表单数据
      updateFormData("category", validCategory)
    }

    const submissionData = {
      name: formData.name.trim(),
      type: formData.type,
      price: Number.parseFloat(formData.price),
      category: validCategory,
      ...(formData.type === "total" && formData.totalTime ? { totalTime: Number.parseInt(formData.totalTime) } : {}),
    }

    onSubmit(submissionData)
  }

  const canSubmit =
    formData.name.trim() &&
    formData.price &&
    Number.parseFloat(formData.price) > 0 &&
    (formData.type !== "total" || formData.totalTime)

  return (
    <div className="space-y-5">
      {/* 快速选择 */}
      {showQuickSelect && (
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            <div className="w-1 h-6 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full" />
            <h2 className="text-lg font-medium bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">快速选择</h2>
            <div className="flex-1 h-px bg-gradient-to-r from-gray-200/50 to-transparent" />
          </div>
          <div className="grid grid-cols-2 gap-3">
            {PRESET_SUBSCRIPTIONS.map((preset, index) => (
              <Card
                key={index}
                className="relative glass-card p-2.5 cursor-pointer hover:scale-[1.02] transition-all duration-150 hover:shadow-xl border border-white/20 hover:border-white/40 group overflow-hidden"
                onClick={() => selectPreset(preset)}
              >
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <div className="relative z-10 flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-1.5">
                      <h4 className="font-medium text-foreground text-xs truncate">{preset.name}</h4>
                      <Badge variant="outline" className="text-[9px] px-1 py-0 shrink-0 border border-border/50 h-4 bg-white/10">
                        {preset.category}
                      </Badge>
                    </div>
                  </div>
                  <div className="ml-2 shrink-0">
                    <p className="text-xs font-medium bg-gradient-to-r from-orange-400 to-yellow-500 bg-clip-text text-transparent whitespace-nowrap">
                      {getCurrencySymbol(currency)}{preset.price}/{preset.type === "monthly" ? "月" : "年"}
                    </p>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      )}

      {/* 自定义表单 */}
      <div className="relative glass-card rounded-2xl p-4 space-y-3 border border-white/20 shadow-xl overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-purple-500/10 to-transparent rounded-full blur-2xl" />
        <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-500/10 to-transparent rounded-full blur-xl" />
        
        <div className="relative z-10 flex items-center space-x-3">
          <div className="w-1 h-6 bg-gradient-to-b from-purple-500 to-blue-600 rounded-full" />
          <h2 className="text-lg font-medium bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">{title}</h2>
          <div className="flex-1 h-px bg-gradient-to-r from-gray-200/50 to-transparent" />

          {/* 保存按钮 */}
          <button
            onClick={handleSubmit}
            disabled={!canSubmit}
            className="relative h-8 w-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed shadow-lg text-white hover:shadow-xl hover:scale-105 transition-all duration-200 overflow-hidden"
            title={submitButtonText}
          >
            {/* 按钮内部光效 */}
            <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent rounded-full" />
            <Save className="relative z-10 h-4 w-4" />
          </button>

          </div>

        {/* 基本信息 */}
        <div className="relative z-10 space-y-2.5">
          {/* 服务名称 - 单行布局 */}
          <div className="flex items-center gap-2">
            <Label htmlFor="name" className="text-xs font-medium w-8 shrink-0">
              名称
            </Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => updateFormData("name", e.target.value)}
              placeholder="输入服务名称..."
              className="h-8 glass-card border-0 focus:ring-2 focus:ring-blue-400/30 flex-1 text-center"
            />
          </div>

          {/* 价格和计费方式 - 单行布局 */}
          <div className="grid grid-cols-2 gap-3">
            <div className="flex items-center gap-2">
              <Label htmlFor="price" className="text-xs font-medium w-8 shrink-0">
                价格
              </Label>
              <div className="relative flex-1">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="price"
                  type="number"
                  value={formData.price}
                  onChange={(e) => updateFormData("price", e.target.value)}
                  placeholder="0.00"
                  step="0.01"
                  className="h-8 pl-10 glass-card border-0 focus:ring-2 focus:ring-blue-400/30"
                />
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Label className="text-xs font-medium w-8 shrink-0">方式</Label>
              <select
                value={formData.type}
                onChange={(e) => updateFormData("type", e.target.value)}
                className="h-8 flex-1 glass-card border-0 rounded-lg px-3 bg-transparent focus:ring-2 focus:ring-blue-400/30"
              >
                <option value="monthly">每月</option>
                <option value="yearly">每年</option>
                <option value="total">一次性</option>
              </select>
            </div>
          </div>

          {/* 使用时长 - 条件显示 */}
          {formData.type === "total" && (
            <div className="grid grid-cols-2 gap-3">
              <div className="flex items-center gap-2">
                <Label htmlFor="totalTime" className="text-xs font-medium w-8 shrink-0">
                  时长
                </Label>
                <div className="relative flex-1">
                  <Input
                    id="totalTime"
                    type="number"
                    value={formData.totalTime}
                    onChange={(e) => updateFormData("totalTime", e.target.value)}
                    placeholder="12"
                    className="h-8 pr-8 glass-card border-0 focus:ring-2 focus:ring-blue-400/30 text-center"
                  />
                  <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-muted-foreground pointer-events-none">月</span>
                </div>
              </div>
              <div></div>
            </div>
          )}
        </div>

        {/* 分类 */}
        <div className="relative z-10 space-y-2">
          <div className="flex items-center justify-between">
            <Label className="text-xs font-medium">分类</Label>
            {deleteMode && (
              <button
                onClick={exitDeleteMode}
                className="text-xs text-blue-500 hover:text-blue-600 font-medium"
              >
                完成
              </button>
            )}
          </div>

          {/* 分类选择 */}
          <div className="flex flex-wrap gap-2">
            {categories.map((category, index) => (
              <div key={category} className="relative">
                <button
                  onClick={() => handleCategoryClick(category, index)}
                  onMouseDown={() => handleMouseDown(index)}
                  onMouseUp={handleMouseUp}
                  onMouseLeave={handleMouseLeave}
                  onTouchStart={() => handleMouseDown(index)}
                  onTouchEnd={handleMouseUp}
                  className={`relative h-7 px-2.5 py-0.5 rounded-full text-xs font-medium transition-all duration-150 overflow-hidden whitespace-nowrap text-ellipsis max-w-[100px] flex items-center justify-center ${
                    formData.category === category && !deleteMode
                      ? "bg-blue-500 text-white shadow-sm transform scale-95"
                      : "bg-white/10 text-muted-foreground hover:text-foreground hover:bg-white/15 active:bg-white/20 active:transform active:scale-95 border border-muted-foreground/40 hover:border-muted-foreground/60"
                  }`}
                  title={deleteMode ? "点击删除" : category}
                  style={{
                    WebkitTapHighlightColor: 'transparent',
                  }}
                >
                  {/* iOS 风格的微妙内阴影 */}
                  {formData.category === category && !deleteMode && (
                    <div className="absolute inset-0 bg-black/5 rounded-full" />
                  )}
                  <span className="relative z-10 truncate leading-none">{category}</span>
                </button>
                
                {/* 删除按钮 - 只在删除模式下显示，且不能删除"其他"分类或正在使用的分类 */}
                {deleteMode && category !== "其他" && categories.length > 1 && !isCategoryInUse(category) && (
                  <button
                    onClick={() => deleteCategory(category)}
                    className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs font-bold shadow-lg hover:bg-red-600 transition-colors z-20"
                    style={{ fontSize: '10px' }}
                  >
                    ×
                  </button>
                )}

                {/* 使用中标识 - 在删除模式下显示正在使用的分类 */}
                {deleteMode && isCategoryInUse(category) && (
                  <div
                    className="absolute -top-1 -right-1 w-5 h-5 bg-orange-500 text-white rounded-full flex items-center justify-center text-xs font-bold shadow-lg z-20"
                    style={{ fontSize: '8px' }}
                    title="该分类正在被支出使用，无法删除"
                  >
                    !
                  </div>
                )}
              </div>
            ))}

            {/* 添加新分类按钮 */}
            {newCategoryName ? (
              <div className="flex items-center gap-1">
                <Input
                  value={newCategoryName}
                  onChange={(e) => setNewCategoryName(e.target.value)}
                  placeholder="新分类"
                  className="h-7 w-20 text-xs bg-white/10 border-0 rounded-full text-center focus:bg-white/15 focus:outline-none focus:ring-2 focus:ring-blue-400/30 placeholder:text-muted-foreground/60"
                  onKeyDown={(e) => {
                    if (e.key === "Enter") addCategory()
                    if (e.key === "Escape") setNewCategoryName("")
                  }}
                  onBlur={() => {
                    if (!newCategoryName.trim()) setNewCategoryName("")
                  }}
                  autoFocus
                />
                <Button
                  onClick={addCategory}
                  size="sm"
                  variant="ghost"
                  className="h-7 w-7 p-0 text-green-500 hover:text-green-400 hover:bg-green-500/10 rounded-full active:scale-90 transition-all duration-150"
                  disabled={!newCategoryName.trim() || categories.includes(newCategoryName.trim())}
                  style={{ WebkitTapHighlightColor: 'transparent' }}
                >
                  <Check className="h-3.5 w-3.5" />
                </Button>
              </div>
            ) : (
              <button
                onClick={() => setNewCategoryName(" ")}
                className="h-7 px-2.5 py-0.5 rounded-full text-xs font-medium transition-all duration-150 bg-white/10 text-muted-foreground hover:text-foreground hover:bg-white/15 border border-dashed border-white/30 hover:border-white/50 flex items-center justify-center active:transform active:scale-95"
                title="添加新分类"
                style={{ WebkitTapHighlightColor: 'transparent' }}
              >
                <Plus className="h-3 w-3" />
              </button>
            )}
          </div>
        </div>


      </div>

      {/* 编辑分类弹窗 */}
      <Dialog open={!!editingCategory} onOpenChange={(open) => !open && cancelEdit()}>
        <DialogContent className="glass-card border-white/20 max-w-md">
          <DialogHeader>
            <DialogTitle className="text-lg font-medium text-foreground">编辑分类</DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="category-name" className="text-xs font-medium">
                分类名称
              </Label>
              <Input
                id="category-name"
                value={editingCategory?.name || ""}
                onChange={(e) => updateEditingName(e.target.value)}
                placeholder="输入分类名称"
                className="glass-card border-0"
                onKeyDown={(e) => {
                  if (e.key === "Enter") confirmEdit()
                  if (e.key === "Escape") cancelEdit()
                }}
                autoFocus
              />
              {editingCategory?.name.trim() && categories.includes(editingCategory.name.trim()) && editingCategory.name !== editingCategory.originalName && (
                <p className="text-xs text-destructive">该分类名称已存在</p>
              )}
            </div>
          </div>

          <div className="flex justify-between">
            <button
              onClick={deleteEditingCategory}
              disabled={editingCategory?.originalName === "其他" || !!(editingCategory?.originalName && isCategoryInUse(editingCategory.originalName))}
              className="flex items-center space-x-2 px-4 py-2 rounded-full glass-card border-0 text-destructive hover:scale-105 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg"
              title={
                editingCategory?.originalName === "其他"
                  ? "不能删除'其他'分类"
                  : (editingCategory?.originalName && isCategoryInUse(editingCategory.originalName))
                    ? "该分类正在被支出使用，无法删除"
                    : "删除分类"
              }
            >
              <Trash2 className="h-4 w-4" />
              <span className="text-sm font-medium">删除</span>
            </button>

            <div className="flex space-x-2">
              <button
                onClick={cancelEdit}
                className="px-4 py-2 rounded-full glass-card border-0 text-muted-foreground hover:text-foreground hover:scale-105 transition-all duration-300 shadow-lg"
              >
                <span className="text-sm font-medium">取消</span>
              </button>
              <button
                onClick={confirmEdit}
                disabled={
                  !editingCategory?.name.trim() ||
                  (categories.includes(editingCategory.name.trim()) && editingCategory.name !== editingCategory.originalName)
                }
                className="px-4 py-2 glass-button rounded-full disabled:opacity-50 disabled:cursor-not-allowed shadow-lg text-white font-medium"
              >
                <span className="text-sm font-medium">确认</span>
              </button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
