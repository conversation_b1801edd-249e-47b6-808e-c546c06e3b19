"use client"

import React, { useState } from "react"
import { Trash2 } from "lucide-react"

interface ClickableBarChartProps {
  subscriptions: Array<{
    id: string
    name: string
    type: "total" | "monthly" | "yearly"
    price: number
    category?: string
  }>
  displayCosts: number[]
  currencySymbol: string
  timeUnit: string
  onItemClick: (subscription: any) => void
  onItemDelete: (id: string) => void
}

export default function ClickableBarChart({
  subscriptions,
  displayCosts,
  currencySymbol,
  timeUnit,
  onItemClick,
  onItemDelete
}: ClickableBarChartProps) {
  const [swipedItem, setSwipedItem] = useState<string | null>(null)
  // 创建数据数组并按金额排序
  const chartData = subscriptions
    .map((sub, index) => ({
      ...sub,
      displayCost: displayCosts[index],
    }))
    .sort((a, b) => b.displayCost - a.displayCost)

  // 找到最大值用于计算比例
  const maxCost = Math.max(...chartData.map(item => item.displayCost))

  // 触摸事件处理
  const handleTouchStart = (e: React.TouchEvent, itemId: string) => {
    // 阻止事件冒泡，避免触发页面级别的滑动
    e.stopPropagation()

    const touch = e.touches[0]
    const startX = touch.clientX
    const startY = touch.clientY
    let hasMoved = false

    const handleTouchMove = (e: TouchEvent) => {
      const touch = e.touches[0]
      const deltaX = touch.clientX - startX
      const deltaY = touch.clientY - startY

      // 如果垂直滑动距离大于水平滑动距离，则不处理
      if (Math.abs(deltaY) > Math.abs(deltaX)) {
        return
      }

      hasMoved = true
      e.preventDefault()
    }

    const handleTouchEnd = (e: TouchEvent) => {
      const touch = e.changedTouches[0]
      const deltaX = touch.clientX - startX
      const deltaY = touch.clientY - startY

      // 只有在水平滑动时才处理
      if (Math.abs(deltaY) <= Math.abs(deltaX) && hasMoved) {
        // 左滑超过50px显示删除按钮
        if (deltaX < -100) {
          setSwipedItem(itemId)
        } else if (deltaX > 100) {
          setSwipedItem(null)
        }
      }

      document.removeEventListener('touchmove', handleTouchMove)
      document.removeEventListener('touchend', handleTouchEnd)
    }

    document.addEventListener('touchmove', handleTouchMove, { passive: false })
    document.addEventListener('touchend', handleTouchEnd)
  }

  // 预定义颜色池
  const colorPalette = [
    "bg-pink-500",    // 粉色
    "bg-purple-500",  // 紫色
    "bg-green-500",   // 绿色
    "bg-blue-500",    // 蓝色
    "bg-cyan-500",    // 青色
    "bg-yellow-500",  // 黄色
    "bg-orange-500",  // 橙色
    "bg-gray-500",    // 灰色
    "bg-red-500",     // 红色
    "bg-violet-500",  // 紫罗兰
    "bg-emerald-500", // 翠绿
    "bg-sky-500",     // 天蓝
    "bg-amber-500",   // 琥珀
    "bg-rose-500",    // 玫瑰红
    "bg-indigo-500",  // 靛蓝
    "bg-lime-500",    // 酸橙绿
  ]

  // 获取分类颜色
  const getCategoryColor = (category?: string) => {
    if (!category) return "bg-blue-500"

    // 预设分类的固定颜色
    const predefinedColors: Record<string, string> = {
      "娱乐": "bg-pink-500",
      "音乐": "bg-purple-500",
      "健康": "bg-green-500",
      "工具": "bg-blue-500",
      "存储": "bg-cyan-500",
      "学习": "bg-yellow-500",
      "生活": "bg-orange-500",
      "其他": "bg-gray-500"
    }

    // 如果是预设分类，返回固定颜色
    if (predefinedColors[category]) {
      return predefinedColors[category]
    }

    // 对于自定义分类，基于分类名称生成一致的颜色
    let hash = 0
    for (let i = 0; i < category.length; i++) {
      const char = category.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }

    // 使用hash值选择颜色，确保同一分类总是得到相同颜色
    const colorIndex = Math.abs(hash) % colorPalette.length
    return colorPalette[colorIndex]
  }

  return (
    <div className="glass-card p-4 rounded-2xl border border-white/20 clickable-bar-chart" data-swipeable="false">
      <div className="space-y-3">
        {chartData.map((item, index) => {
          const percentage = maxCost > 0 ? (item.displayCost / maxCost) * 100 : 0
          const isSwipedLeft = swipedItem === item.id

          return (
            <div key={item.id} className="relative overflow-hidden rounded-lg">
              {/* 删除按钮背景 - 只有在左滑时才显示 */}
              {isSwipedLeft && (
                <div className="absolute right-0 top-0 bottom-0 w-20 bg-red-500 flex items-center justify-center">
                  <button
                    onClick={() => onItemDelete(item.id)}
                    className="text-white hover:text-red-200 transition-colors"
                  >
                    <Trash2 className="h-5 w-5" />
                  </button>
                </div>
              )}

              {/* 主卡片 */}
              <div
                className="flex items-center space-x-3 cursor-pointer hover:bg-white/5 p-2 rounded-lg transition-all duration-300 bg-white/10"
                style={{ transform: isSwipedLeft ? 'translateX(-80px)' : 'translateX(0px)' }}
                onClick={() => onItemClick(item)}
                onTouchStart={(e) => handleTouchStart(e, item.id)}
              >
                {/* 排名 */}
                <div className="w-6 text-center">
                  <span className="text-xs font-medium text-muted-foreground">#{index + 1}</span>
                </div>

                {/* 条形图 */}
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-foreground truncate max-w-32">{item.name}</span>
                      {item.category && (
                        <span className="text-xs px-1.5 py-0.5 rounded bg-blue-500 text-white shrink-0">
                          {item.category}
                        </span>
                      )}
                    </div>
                    <div className="text-sm font-medium text-foreground ml-2">
                      {currencySymbol}{item.displayCost.toFixed(2)}<span className="text-xs text-muted-foreground">/{timeUnit}</span>
                    </div>
                  </div>
                  <div className="relative">
                    <div className="w-full bg-white/10 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-500 ${getCategoryColor(item.category)}`}
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}