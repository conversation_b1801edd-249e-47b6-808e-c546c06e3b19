"use client"

import React from "react"
import UnifiedLink from "./unified-link"

interface MobileLinkProps {
  href: string
  children: React.ReactNode
  className?: string
  onClick?: (e: React.MouseEvent) => void
}

export default function MobileLink({ href, children, className, onClick }: MobileLinkProps) {
  return (
    <UnifiedLink href={href} className={className} onClick={onClick}>
      {children}
    </UnifiedLink>
  )
}
