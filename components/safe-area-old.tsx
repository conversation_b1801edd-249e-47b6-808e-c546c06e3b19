"use client"

import React, { useEffect, useState } from 'react'
import { Capacitor } from '@capacitor/core'
import { StatusBar } from '@capacitor/status-bar'
import { getStatusBarHeight } from './capacitor-initializer'

interface SafeAreaProps {
  children: React.ReactNode
  className?: string
  mode?: 'top' | 'bottom' | 'both' | 'inset'
  statusBarHeight?: number
}

export default function SafeArea({ children, className = '', mode = 'both', statusBarHeight }: SafeAreaProps) {
  const [safeAreaInsets, setSafeAreaInsets] = useState({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  })

  useEffect(() => {
    const updateSafeAreaInsets = () => {
      if (typeof window === 'undefined') return
      
      let topInset = 0
      let bottomInset = 0
      let leftInset = 0
      let rightInset = 0

      // 如果是 Capacitor 原生环境，优先使用状态栏高度
      if (Capacitor.isNativePlatform()) {
        topInset = getStatusBarHeight()
      } else {
        // Web 环境使用 CSS 环境变量
        if ('CSS' in window && CSS.supports('padding-top', 'env(safe-area-inset-top)')) {
          const parsePixels = (value: string) => {
            const match = value.match(/(\d+(?:\.\d+)?)px/)
            return match ? parseFloat(match[1]) : 0
          }
          
          topInset = parsePixels(getComputedStyle(document.documentElement).getPropertyValue('--sat') || '0px')
          rightInset = parsePixels(getComputedStyle(document.documentElement).getPropertyValue('--sar') || '0px')
          bottomInset = parsePixels(getComputedStyle(document.documentElement).getPropertyValue('--sab') || '0px')
          leftInset = parsePixels(getComputedStyle(document.documentElement).getPropertyValue('--sal') || '0px')
        }
      }

      setSafeAreaInsets({
        top: topInset,
        right: rightInset,
        bottom: bottomInset,
        left: leftInset
      })
    }

    updateSafeAreaInsets()
    
    // 监听窗口大小变化和状态栏变化
    window.addEventListener('resize', updateSafeAreaInsets)
    
    // 定期检查状态栏高度（针对Android）
    const interval = setInterval(updateSafeAreaInsets, 1000)
    
    return () => {
      window.removeEventListener('resize', updateSafeAreaInsets)
      clearInterval(interval)
    }
  }, [])

  const getSafeAreaStyle = () => {
    const style: React.CSSProperties = {}
    
    if (mode === 'top' || mode === 'both' || mode === 'inset') {
      // 优先使用传入的状态栏高度，否则使用安全区域插入值
      const finalTopHeight = statusBarHeight || safeAreaInsets.top
      style.paddingTop = `${finalTopHeight}px`
    }
    
    if (mode === 'bottom' || mode === 'both' || mode === 'inset') {
      style.paddingBottom = `${safeAreaInsets.bottom}px`
    }
    
    if (mode === 'inset') {
      style.paddingLeft = `${safeAreaInsets.left}px`
      style.paddingRight = `${safeAreaInsets.right}px`
    }
    
    return style
  }

  return (
    <div 
      className={className}
      style={getSafeAreaStyle()}
    >
      {children}
    </div>
  )
}