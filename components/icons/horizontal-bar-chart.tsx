import React from 'react'

interface HorizontalBarChartProps {
  className?: string
}

export const HorizontalBarChart: React.FC<HorizontalBarChartProps> = ({ className = "h-4 w-4" }) => {
  return (
    <svg
      className={className}
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      {/* 第一条（最长） */}
      <rect x="2" y="5" width="18" height="1" rx="0.5" />
      {/* 第二条（中等） */}
      <rect x="2" y="11.5" width="12" height="1" rx="0.5" />
      {/* 第三条（最短） */}
      <rect x="2" y="18" width="8" height="1" rx="0.5" />
    </svg>
  )
}

export default HorizontalBarChart
