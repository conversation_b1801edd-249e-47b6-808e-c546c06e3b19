"use client"

import React from "react"
import { <PERSON><PERSON><PERSON> as RechartsPieChart, Pie, Cell, ResponsiveContainer } from "recharts"
import { navigateToRanking } from '@/lib/navigation'

interface PieChartProps {
  subscriptions: Array<{
    id: string
    name: string
    type: "total" | "monthly" | "yearly"
    price: number
    category?: string
  }>
  displayCosts: number[]
  currencySymbol: string
  timeUnit: string
}

export default function PieChart({ subscriptions, displayCosts, currencySymbol, timeUnit }: PieChartProps) {

  // 按分类统计数据
  const categoryStats = subscriptions.reduce((acc, sub, index) => {
    const category = sub.category || "其他"
    const cost = displayCosts[index]

    if (!acc[category]) {
      acc[category] = {
        name: category, // Recharts需要name字段
        value: 0,
        count: 0,
        items: []
      }
    }

    acc[category].value += cost
    acc[category].count += 1
    acc[category].items.push({ name: sub.name, cost })

    return acc
  }, {} as Record<string, { name: string; value: number; count: number; items: Array<{ name: string; cost: number }> }>)

  const chartData = Object.values(categoryStats).sort((a, b) => b.value - a.value)
  const totalValue = chartData.reduce((sum, item) => sum + item.value, 0)

  // 点击事件处理 - 移动端兼容
  const handleCategoryClick = (category: string) => {
    console.log('PieChart: 点击分类:', category)
    navigateToRanking(category)
  }

  // 点击空白区域显示所有固定支出 - 移动端兼容
  const handleBackgroundClick = (e: React.MouseEvent) => {
    // 检查点击的是否是背景区域（不是饼图或图例）
    const target = e.target as HTMLElement
    if (target.closest('.recharts-pie-sector') || target.closest('.legend-item')) {
      return // 如果点击的是饼图扇形或图例项，不处理
    }
    
    navigateToRanking('全部')
  }

  // 预定义颜色池
  const colorPalette = [
    "#ec4899", // 粉色
    "#a855f7", // 紫色
    "#10b981", // 绿色
    "#3b82f6", // 蓝色
    "#06b6d4", // 青色
    "#eab308", // 黄色
    "#f97316", // 橙色
    "#6b7280", // 灰色
    "#ef4444", // 红色
    "#8b5cf6", // 紫罗兰
    "#059669", // 翠绿
    "#0ea5e9", // 天蓝
    "#d97706", // 琥珀
    "#dc2626", // 深红
    "#7c3aed", // 靛蓝
    "#16a34a", // 草绿
  ]

  // 获取分类颜色
  const getCategoryColor = (category: string) => {
    // 预设分类的固定颜色
    const predefinedColors: Record<string, string> = {
      "娱乐": "#ec4899",
      "音乐": "#a855f7",
      "健康": "#10b981",
      "工具": "#3b82f6",
      "存储": "#06b6d4",
      "学习": "#eab308",
      "生活": "#f97316",
      "其他": "#6b7280"
    }

    // 如果是预设分类，返回固定颜色
    if (predefinedColors[category]) {
      return predefinedColors[category]
    }

    // 对于自定义分类，基于分类名称生成一致的颜色
    let hash = 0
    for (let i = 0; i < category.length; i++) {
      const char = category.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }

    // 使用hash值选择颜色，确保同一分类总是得到相同颜色
    const colorIndex = Math.abs(hash) % colorPalette.length
    return colorPalette[colorIndex]
  }

  return (
    <div className="glass-card p-6 rounded-2xl border-0" onClick={handleBackgroundClick}>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
        {/* 左侧饼图 */}
        <div className="flex justify-center">
          <div className="w-80 h-80">
            <ResponsiveContainer width="100%" height="100%">
              <RechartsPieChart>
                <Pie
                  data={chartData}
                  cx="50%"
                  cy="50%"
                  outerRadius={120}
                  innerRadius={0}
                  fill="#8884d8"
                  dataKey="value"
                  onClick={(data) => handleCategoryClick(data.name)}
                  isAnimationActive={true}
                  animationDuration={800}
                >
                  {chartData.map((entry, index) => (
                    <Cell
                      key={`cell-${index}`}
                      fill={getCategoryColor(entry.name)}
                      className="cursor-pointer hover:opacity-80 transition-opacity duration-200"
                    />
                  ))}
                </Pie>
              </RechartsPieChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* 右侧图例 */}
        <div className="space-y-2 min-w-0">
          {chartData.map((item) => {
            const percentage = ((item.value / totalValue) * 100).toFixed(1)
            return (
              <div
                key={item.name}
                className="legend-item flex items-center gap-3 p-2 rounded-lg hover:bg-gray-50/50 cursor-pointer transition-colors duration-200"
                onClick={(e) => {
                  e.stopPropagation()
                  handleCategoryClick(item.name)
                }}
              >
                {/* 百分比 */}
                <div className="text-sm font-medium text-gray-600 w-12 text-right flex-shrink-0">
                  {percentage}%
                </div>

                {/* 颜色 */}
                <div
                  className="w-3 h-3 rounded-full flex-shrink-0"
                  style={{ backgroundColor: getCategoryColor(item.name) }}
                />

{/* 左侧：分类名称 + 数量 */}
<div className="flex items-center gap-2 min-w-0">
  {/* 分类名称 */}
  <div className="font-medium text-gray-900 truncate">
    {item.name}
  </div>

  {/* 数量 */}
  <div className="text-sm text-gray-500 flex-shrink-0">
    {item.count}个
  </div>
</div>

{/* 中间撑开 */}
<div className="flex-1" />

{/* 右侧：支出金额（保持右对齐） */}
<div className="text-xs font-medium text-gray-900 text-right min-w-[80px]">
  {currencySymbol}{item.value.toFixed(2)}/{timeUnit}
</div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}
