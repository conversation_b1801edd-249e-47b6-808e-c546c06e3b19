# MuMu 模拟器 ADB 调试配置指南

## 概述
本文档记录了如何配置 ADB 连接 MuMu 模拟器进行安卓应用调试的完整流程。

## 环境要求
- macOS 系统
- MuMu 模拟器已安装
- Node.js 项目（使用 Capacitor）

## 配置步骤

### 1. 安装 Android SDK 和 ADB

#### 方式一：安装 Android Studio（推荐）
```bash
# 使用 Homebrew 安装 Android Studio
brew install --cask android-studio
```

#### 安装后配置
1. 启动 Android Studio：
   ```bash
   open /Applications/Android\ Studio.app
   ```

2. 在 Android Studio 中完成初始设置：
   - 选择 "Standard" 安装类型
   - 等待 Android SDK 组件下载完成
   - 确保安装了：
     - Android SDK Platform-Tools (包含 ADB)
     - Android 13 (API 33) 或更高版本

### 2. 配置 ADB 路径

#### 将 ADB 添加到系统 PATH
```bash
# 添加到 .zshrc（macOS 默认 shell）
echo 'export PATH=$PATH:/Users/<USER>/Library/Android/sdk/platform-tools' >> ~/.zshrc

# 重新加载配置
source ~/.zshrc
```

#### 验证 ADB 安装
```bash
# 验证 ADB 版本
adb version

# 预期输出：
# Android Debug Bridge version 1.0.41
# Version 36.0.0-13206524
# Installed as /Users/<USER>/Library/Android/sdk/platform-tools/adb
```

### 3. 启动和配置 MuMu 模拟器

#### 启动 MuMu 模拟器
```bash
open /Applications/MuMuPlayer.app
```

#### 在模拟器中开启开发者选项
1. 进入 MuMu 模拟器的 "设置" → "关于手机"
2. 连续点击 "版本号" 7 次开启开发者选项
3. 返回设置，找到 "开发者选项"
4. 开启 "USB 调试" 选项

### 4. 连接 ADB 到 MuMu 模拟器

#### 检查 MuMu 模拟器运行状态
```bash
# 检查 MuMu 进程
ps aux | grep -i mumu

# 预期输出包含：
# MuMuPlaye 29878 fansc   0.4  0.3 412392144  53952   ??  S     1:03上午   0:03.00 /Applications/MuMuPlayer.app/Contents/MacOS/MuMuPlayer
```

#### 检查 MuMu 端口使用情况
```bash
# 检查 MuMu 使用的端口
lsof -i -P | grep -i mumu

# 预期输出包含：
# MuMuPlaye 29878 fansc   10u  IPv4 0xc49a4d08eb029b14      0t0  TCP *:20000 (LISTEN)
# MuMuPlaye 29878 fansc   14u  IPv4 0xf4b3bd7fd22ce0db      0t0  TCP *:21000 (LISTEN)
```

#### 重启 ADB 服务
```bash
# 停止 ADB 服务
adb kill-server

# 启动 ADB 服务
adb start-server

# 预期输出：
# * daemon not running; starting now at tcp:5037
# * daemon started successfully
```

#### 检查设备连接
```bash
# 检查已连接的设备
adb devices

# 预期输出：
# List of devices attached
# emulator-5554	device
```

### 5. 验证连接

#### 测试设备连接
```bash
# 获取设备型号
adb -s emulator-5554 shell getprop ro.product.model

# 预期输出：
# 23127PN0CC
```

#### 检查设备状态
```bash
# 检查设备详细信息
adb devices -l
```

## 常见问题排查

### 1. 设备显示为 offline
```bash
# 解决方案：
# 1. 重启 ADB 服务
adb kill-server && adb start-server

# 2. 重新连接设备
adb connect localhost:20000

# 3. 检查 MuMu 模拟器的开发者选项是否已开启
```

### 2. 连接被拒绝 (Connection refused)
```bash
# 解决方案：
# 1. 确认 MuMu 模拟器正在运行
# 2. 检查开发者选项和 USB 调试是否已开启
# 3. 重启 MuMu 模拟器
# 4. 重启 ADB 服务
```

### 3. ADB 命令未找到
```bash
# 解决方案：
# 1. 确认 Android Studio 已正确安装
# 2. 检查 PATH 配置是否正确
# 3. 使用完整路径测试：
/Users/<USER>/Library/Android/sdk/platform-tools/adb version
```

## 项目构建和运行

### 构建和同步应用
```bash
# 构建 Next.js 应用
npm run build

# 同步到 Android 平台
npx cap sync android

# 运行应用到模拟器
npx cap run android
```

### 验证应用安装
```bash
# 检查应用是否已安装
adb -s emulator-5554 shell pm list packages | grep -i noover

# 预期输出：
# package:com.noover.app
```

## 实用的 ADB 命令

### 基本命令
```bash
# 查看连接的设备
adb devices

# 查看设备日志
adb logcat

# 安装 APK
adb install app.apk

# 卸载应用
adb uninstall com.example.app

# 进入设备 shell
adb shell
```

### 调试命令
```bash
# 查看设备信息
adb shell getprop

# 查看运行中的进程
adb shell ps

# 查看应用日志
adb logcat | grep "com.noover.app"

# 截图
adb shell screencap -p /sdcard/screenshot.png
adb pull /sdcard/screenshot.png
```

## 总结

通过以上步骤，我们成功配置了 ADB 连接 MuMu 模拟器的环境：

1. ✅ 安装了 Android Studio 和 Android SDK
2. ✅ 配置了 ADB 到系统 PATH
3. ✅ 启动并配置了 MuMu 模拟器
4. ✅ 成功连接 ADB 到 MuMu 模拟器
5. ✅ 构建并安装了应用到模拟器

配置完成后，可以使用标准的 Android 开发工作流进行应用调试和测试。