#!/bin/bash

# NoOver App Icon 一键替换脚本
# 使用方法: ./replace-icons.sh [icons目录路径]
# 例如: ./replace-icons.sh
# 例如: ./replace-icons.sh /path/to/icons

set -e

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 检查参数，如果提供了参数则使用该路径，否则使用默认的icons目录
if [ $# -eq 1 ]; then
    ICONS_DIR="$1"
else
    ICONS_DIR="$SCRIPT_DIR/icons"
fi

echo "🚀 开始替换应用图标..."
echo "图标目录: $ICONS_DIR"
echo ""

# 检查必要的目录是否存在
if [ ! -d "$ICONS_DIR" ]; then
    echo "错误: icons目录不存在: $ICONS_DIR"
    exit 1
fi

if [ ! -d "$ICONS_DIR/android" ]; then
    echo "错误: android子目录不存在: $ICONS_DIR/android"
    exit 1
fi

if [ ! -d "$ICONS_DIR/ios" ]; then
    echo "错误: ios子目录不存在: $ICONS_DIR/ios"
    exit 1
fi

# 函数：复制文件
copy_file() {
    local source="$1"
    local target="$2"
    
    # 创建目标目录（如果不存在）
    mkdir -p "$(dirname "$target")"
    
    # 复制文件
    if [ -f "$source" ]; then
        cp "$source" "$target"
        echo "✅ 复制: $source -> $target"
    else
        echo "⚠️  源文件不存在: $source"
    fi
}

# 函数：更新Android项目图标
update_android_icons() {
    echo "🤖 更新Android项目图标..."
    
    # Android图标密度列表
    for density in mdpi hdpi xhdpi xxhdpi xxxhdpi ldpi; do
        source_icon="$ICONS_DIR/android/mipmap-$density/ic_launcher.png"
        
        # 主图标
        target="$SCRIPT_DIR/android/app/src/main/res/mipmap-$density/ic_launcher.png"
        copy_file "$source_icon" "$target"
        
        # 圆形图标
        target_round="$SCRIPT_DIR/android/app/src/main/res/mipmap-$density/ic_launcher_round.png"
        copy_file "$source_icon" "$target_round"
        
        # 前景图标
        target_foreground="$SCRIPT_DIR/android/app/src/main/res/mipmap-$density/ic_launcher_foreground.png"
        copy_file "$source_icon" "$target_foreground"
    done
}

# 函数：更新iOS项目图标
update_ios_icons() {
    echo "🍎 更新iOS项目图标..."
    
    # iOS图标文件列表
    ios_icons=(
        "<EMAIL>"
        "<EMAIL>"
        "<EMAIL>"
        "<EMAIL>"
        "<EMAIL>"
        "<EMAIL>"
        "<EMAIL>"
        "<EMAIL>"
        "<EMAIL>"
        "<EMAIL>"
        "<EMAIL>"
        "<EMAIL>"
        "<EMAIL>"
        "<EMAIL>"
        "<EMAIL>"
        "icon-1024.png"
        "<EMAIL>"
    )
    
    for icon_name in "${ios_icons[@]}"; do
        source_icon="$ICONS_DIR/ios/AppIcon.appiconset/$icon_name"
        target="$SCRIPT_DIR/ios/App/App/Assets.xcassets/AppIcon.appiconset/$icon_name"
        copy_file "$source_icon" "$target"
    done
}

# 函数：更新Web项目logo
update_web_logo() {
    echo "🌐 更新Web项目logo..."
    
    source_logo="$ICONS_DIR/ios/AppIcon.appiconset/icon-1024.png"
    target_logo="$SCRIPT_DIR/public/logo.png"
    
    # 创建目标目录（如果不存在）
    mkdir -p "$(dirname "$target_logo")"
    
    # 复制文件
    if [ -f "$source_logo" ]; then
        cp "$source_logo" "$target_logo"
        echo "✅ 复制logo: $source_logo -> $target_logo"
    else
        echo "⚠️  源logo文件不存在: $source_logo"
    fi
}

# 执行更新
update_android_icons
update_ios_icons
update_web_logo

echo ""
echo "🎉 应用图标替换完成！"
echo ""
echo "下一步操作："
echo "1. 重新构建APK: cd android && ./gradlew assembleDebug"
echo "2. 或者在Xcode中重新构建iOS应用"
echo "3. 重新构建Web应用: npm run build && npm run export"
echo ""
echo "注意：如果需要查看更改，请重新构建相应的应用包。"