"use client"

import { useEffect, useRef } from "react"
import { type TimeView } from "@/lib/time-config"

interface UsePageSwipeProps {
  currentTimeView: TimeView
  onTimeViewChange: (timeView: TimeView) => void
  enabled?: boolean
}

const timeViewOrder: TimeView[] = ["daily", "weekly", "monthly", "yearly"]

export function usePageSwipe({ 
  currentTimeView, 
  onTimeViewChange, 
  enabled = true 
}: UsePageSwipeProps) {
  const startX = useRef(0)
  const startY = useRef(0)
  const isDragging = useRef(false)

  useEffect(() => {
    if (!enabled) return

    const handleTouchStart = (e: TouchEvent) => {
      // 检查触摸是否发生在条形图区域内
      const target = e.target as HTMLElement
      if (target.closest('.clickable-bar-chart') || target.closest('[data-swipeable="false"]')) {
        isDragging.current = false
        return
      }

      startX.current = e.touches[0].clientX
      startY.current = e.touches[0].clientY
      isDragging.current = true
    }

    const handleTouchMove = (e: TouchEvent) => {
      if (!isDragging.current) return
      
      // 防止页面滚动时的误触发
      const deltaX = Math.abs(e.touches[0].clientX - startX.current)
      const deltaY = Math.abs(e.touches[0].clientY - startY.current)
      
      // 如果垂直滑动距离大于水平滑动距离，不处理
      if (deltaY > deltaX) {
        return
      }
      
      // 如果是水平滑动，阻止默认的滚动行为
      if (deltaX > 10) {
        e.preventDefault()
      }
    }

    const handleTouchEnd = (e: TouchEvent) => {
      if (!isDragging.current) return
      
      const endX = e.changedTouches[0].clientX
      const endY = e.changedTouches[0].clientY
      const deltaX = endX - startX.current
      const deltaY = endY - startY.current
      
      // 只有在水平滑动距离大于垂直滑动距离时才处理
      if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
        const currentIndex = timeViewOrder.indexOf(currentTimeView)
        
        if (deltaX > 0 && currentIndex < timeViewOrder.length - 1) {
          // 向右滑动，日向年（daily → weekly → monthly → yearly）
          onTimeViewChange(timeViewOrder[currentIndex + 1])
        } else if (deltaX < 0 && currentIndex > 0) {
          // 向左滑动，年向日（yearly → monthly → weekly → daily）
          onTimeViewChange(timeViewOrder[currentIndex - 1])
        }
      }
      
      isDragging.current = false
    }

    // 鼠标事件支持（桌面端）
    const handleMouseDown = (e: MouseEvent) => {
      // 检查鼠标是否在条形图区域内
      const target = e.target as HTMLElement
      if (target.closest('.clickable-bar-chart') || target.closest('[data-swipeable="false"]')) {
        isDragging.current = false
        return
      }

      startX.current = e.clientX
      startY.current = e.clientY
      isDragging.current = true
    }

    const handleMouseMove = (e: MouseEvent) => {
      if (!isDragging.current) return
      
      const deltaX = Math.abs(e.clientX - startX.current)
      const deltaY = Math.abs(e.clientY - startY.current)
      
      // 如果垂直移动距离大于水平移动距离，不处理
      if (deltaY > deltaX) {
        return
      }
    }

    const handleMouseUp = (e: MouseEvent) => {
      if (!isDragging.current) return
      
      const endX = e.clientX
      const endY = e.clientY
      const deltaX = endX - startX.current
      const deltaY = endY - startY.current
      
      // 只有在水平滑动距离大于垂直滑动距离时才处理
      if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
        const currentIndex = timeViewOrder.indexOf(currentTimeView)
        
        if (deltaX > 0 && currentIndex < timeViewOrder.length - 1) {
          // 向右滑动，日向年（daily → weekly → monthly → yearly）
          onTimeViewChange(timeViewOrder[currentIndex + 1])
        } else if (deltaX < 0 && currentIndex > 0) {
          // 向左滑动，年向日（yearly → monthly → weekly → daily）
          onTimeViewChange(timeViewOrder[currentIndex - 1])
        }
      }
      
      isDragging.current = false
    }

    // 添加事件监听器
    document.addEventListener('touchstart', handleTouchStart, { passive: false })
    document.addEventListener('touchmove', handleTouchMove, { passive: false })
    document.addEventListener('touchend', handleTouchEnd, { passive: false })
    
    document.addEventListener('mousedown', handleMouseDown)
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseup', handleMouseUp)

    // 清理事件监听器
    return () => {
      document.removeEventListener('touchstart', handleTouchStart)
      document.removeEventListener('touchmove', handleTouchMove)
      document.removeEventListener('touchend', handleTouchEnd)
      
      document.removeEventListener('mousedown', handleMouseDown)
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
    }
  }, [currentTimeView, onTimeViewChange, enabled])

  return null
}
