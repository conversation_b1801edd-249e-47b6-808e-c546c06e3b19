Android 异形屏适配方案
获取状态栏高度
使用 StatusBar.currentHeight 获取状态栏高度并手动调整布局：

jsx
import { StatusBar, View, Platform } from 'react-native';

const statusBarHeight = Platform.OS === 'android' 
  ? StatusBar.currentHeight 
  : 0;

<View style={{ paddingTop: statusBarHeight }}>
  <Text>Android 内容</Text>
</View>
156

启用沉浸式状态栏
在 android/app/src/main/res/values/styles.xml 中配置：

xml
<style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
  <item name="android:windowTranslucentStatus">true</item> <!-- 半透明状态栏 -->
</style>
需配合 paddingTop: StatusBar.currentHeight 避免内容遮挡