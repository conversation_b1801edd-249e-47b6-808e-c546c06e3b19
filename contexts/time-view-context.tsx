"use client"

import React, { createContext, useContext, useState, useEffect } from "react"
import { type TimeView } from "@/lib/time-config"

interface TimeViewContextType {
  timeView: TimeView
  setTimeView: (view: TimeView) => void
}

const TimeViewContext = createContext<TimeViewContextType | undefined>(undefined)

export function TimeViewProvider({ children }: { children: React.ReactNode }) {
  const [timeView, setTimeViewState] = useState<TimeView>("daily")

  // 从 localStorage 加载保存的时间视图
  useEffect(() => {
    const savedTimeView = localStorage.getItem("globalTimeView")
    if (savedTimeView && ["daily", "weekly", "monthly", "yearly"].includes(savedTimeView)) {
      setTimeViewState(savedTimeView as TimeView)
    }
  }, [])

  // 设置时间视图并保存到 localStorage
  const setTimeView = (view: TimeView) => {
    setTimeViewState(view)
    localStorage.setItem("globalTimeView", view)
  }

  return (
    <TimeViewContext.Provider value={{ timeView, setTimeView }}>
      {children}
    </TimeViewContext.Provider>
  )
}

export function useTimeView() {
  const context = useContext(TimeViewContext)
  if (context === undefined) {
    throw new Error("useTimeView must be used within a TimeViewProvider")
  }
  return context
}
