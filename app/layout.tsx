import type React from "react"
import type { Metadata, Viewport } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { TimeViewProvider } from "@/contexts/time-view-context"
import CapacitorInitializer from "@/components/capacitor-initializer"
import <PERSON>CHandler from "@/components/rsc-handler"
import SafeArea from "@/components/safe-area"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "固定支出计算 - 记录现代人的经济压力",
  description: "帮助用户警醒和管理各种固定支出的经济负担",
  generator: 'v0.dev',
}

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  viewportFit: 'cover'
}

function LayoutContent({ children }: { children: React.ReactNode }) {
  return (
    <div className="max-w-sm mx-auto bg-white min-h-screen gesture-area">
      {/* 顶部安全区域适配 */}
      <SafeArea mode="top" className="bg-white" />
      {/* 主要内容区域 */}
      <div className="flex-1">
        {children}
      </div>
      {/* 底部安全区域适配 */}
      <SafeArea mode="bottom" className="bg-white" />
    </div>
  )
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="NoOver" />
        <meta name="theme-color" content="#ffffff" />
        
        {/* Icons */}
        <link rel="icon" href="/logo.png" type="image/png" />
        <link rel="apple-touch-icon" href="/logo.png" />
        <link rel="shortcut icon" href="/logo.png" type="image/png" />
      </head>
      <body className={`${inter.className} antialiased`}>
        <CapacitorInitializer />
        <RSCHandler />
        <TimeViewProvider>
          <LayoutContent>{children}</LayoutContent>
        </TimeViewProvider>
      </body>
    </html>
  )
}
