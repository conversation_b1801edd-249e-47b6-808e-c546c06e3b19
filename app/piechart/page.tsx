"use client"

import { useEffect, useState } from "react"
import AppHeader from "@/components/app-header"
import PieChart from "@/components/pie-chart"
import ButtonGroup from "@/components/ui/button-group"
import SwipeHint from "@/components/swipe-hint"
import AnimatedViewContainer from "@/components/animated-view-container"
import SettingsModal from "@/components/settings-modal"
import FloatingAddButton from "@/components/floating-add-button"
import { timeLabels, timeUnits } from "@/lib/time-config"
import { useTimeView } from "@/contexts/time-view-context"
import { usePageSwipe } from "@/hooks/use-page-swipe"

interface Subscription {
  id: string
  name: string
  type: "total" | "monthly" | "yearly"
  price: number
  category?: string
  totalTime?: number
}

interface SettingsData {
  currency: string
  income: number
  incomeType: "daily" | "monthly" | "yearly"
  workDays: number
}

export default function PieChartPage() {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([])
  const [settings, setSettings] = useState<SettingsData>({
    currency: "CNY",
    income: 0,
    incomeType: "monthly",
    workDays: 22
  })
  const [showSettings, setShowSettings] = useState(false)
  const { timeView, setTimeView } = useTimeView()

  // 添加页面滑动功能
  usePageSwipe({
    currentTimeView: timeView,
    onTimeViewChange: setTimeView,
    enabled: subscriptions.length > 0 // 只有在有订阅数据时才启用滑动
  })

  // 加载数据
  useEffect(() => {
    const loadData = () => {
      const savedSubscriptions = localStorage.getItem("subscriptions")
      const savedSettings = localStorage.getItem("settings")

      if (savedSubscriptions) {
        setSubscriptions(JSON.parse(savedSubscriptions))
      }

      if (savedSettings) {
        setSettings(JSON.parse(savedSettings))
      }
    }

    // 初始加载
    loadData()

    // 监听storage变化（跨标签页）
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'subscriptions' || e.key === 'settings') {
        loadData()
      }
    }

    // 监听页面可见性变化（移动端返回时刷新）
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        loadData()
      }
    }

    // 监听自定义事件（同页面内数据更新）
    const handleDataUpdate = () => {
      loadData()
    }

    window.addEventListener('storage', handleStorageChange)
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('subscriptionsUpdated', handleDataUpdate)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('subscriptionsUpdated', handleDataUpdate)
    }
  }, [])

  // 保存设置
  const handleSaveSettings = (newSettings: { workDays: number; income: number; incomeType: "yearly" | "monthly" | "daily"; currency: string }) => {
    const updatedSettings = {
      workDays: newSettings.workDays,
      income: newSettings.income,
      incomeType: newSettings.incomeType,
      currency: newSettings.currency
    }
    setSettings(updatedSettings)
    localStorage.setItem("settings", JSON.stringify(updatedSettings))
  }

  // 获取货币符号
  const getCurrencySymbol = (currency: string) => {
    switch (currency) {
      case "CNY": return "¥"
      case "USD": return "$"
      case "EUR": return "€"
      default: return "¥"
    }
  }

  // 计算显示成本
  const calculateDisplayCost = (sub: Subscription, targetTimeView: string): number => {
    const workDaysPerMonth = settings.workDays
    
    switch (targetTimeView) {
      case "daily":
        switch (sub.type) {
          case "total":
            if (sub.totalTime) {
              return sub.price / (sub.totalTime * workDaysPerMonth)
            }
            return 0
          case "monthly":
            return sub.price / workDaysPerMonth
          case "yearly":
            return sub.price / (workDaysPerMonth * 12)
          default:
            return 0
        }
      case "weekly":
        return calculateDisplayCost(sub, "daily") * 5
      case "monthly":
        switch (sub.type) {
          case "total":
            if (sub.totalTime) {
              return sub.price / sub.totalTime
            }
            return 0
          case "monthly":
            return sub.price
          case "yearly":
            return sub.price / 12
          default:
            return 0
        }
      case "yearly":
        switch (sub.type) {
          case "total":
            if (sub.totalTime) {
              return sub.price / sub.totalTime * 12
            }
            return 0
          case "monthly":
            return sub.price * 12
          case "yearly":
            return sub.price
          default:
            return 0
        }
      default:
        return 0
    }
  }

  // 计算显示成本数组
  const displayCosts = subscriptions.map(sub => calculateDisplayCost(sub, timeView))

  return (
    <div className="min-h-screen bg-background">
      {/* 统一头部导航 */}
      <AppHeader
        currentPage="piechart"
        title="支出分析"
        subtitle={subscriptions.length > 0 ? (
          `${new Set(subscriptions.map(sub => sub.category || "其他")).size} 个分类的详细分布`
        ) : (
          "暂无数据可分析"
        )}
        onSettingsClick={() => setShowSettings(true)}
      />

      {/* 时间视图选择器 - 只在有固定支出时显示 */}
      {subscriptions.length > 0 && (
        <div className="bg-white/5 backdrop-blur-sm border-b border-white/5 px-6 py-4">
          <ButtonGroup
            options={[
              { value: "daily", label: timeLabels.daily },
              { value: "weekly", label: timeLabels.weekly },
              { value: "monthly", label: timeLabels.monthly },
              { value: "yearly", label: timeLabels.yearly },
            ]}
            value={timeView}
            onChange={(value) => setTimeView(value as any)}
          />
        </div>
      )}

      <div className="breathing-space">
        {/* 饼图展示 */}
        <AnimatedViewContainer currentTimeView={timeView} animationType="slide">
          {subscriptions.length > 0 ? (
            <PieChart
              subscriptions={subscriptions}
              displayCosts={displayCosts}
              currencySymbol={getCurrencySymbol(settings.currency)}
              timeUnit={timeUnits[timeView]}
            />
          ) : (
            <div className="glass-card p-8 rounded-2xl border-0 text-center">
              <p className="text-muted-foreground">暂无固定支出数据</p>
            </div>
          )}
        </AnimatedViewContainer>
      </div>

      {/* 设置模态框 */}
      <SettingsModal
        open={showSettings}
        onClose={() => setShowSettings(false)}
        workDaysPerMonth={settings.workDays}
        income={settings.income}
        incomeType={settings.incomeType}
        currency={settings.currency}
        onSave={handleSaveSettings}
      />

      {/* 滑动提示 */}
      <SwipeHint show={subscriptions.length > 0} currentTimeView={timeView} />

      {/* 浮动新增按钮 */}
      <FloatingAddButton />
    </div>
  )
}