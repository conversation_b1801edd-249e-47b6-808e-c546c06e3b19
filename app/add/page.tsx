"use client"

import { useState, useEffect } from "react"
import { navigateBack, navigateToHome } from '@/lib/navigation'
import AppHeader from "@/components/app-header"
import BottomNavigation from "@/components/bottom-navigation"
import SettingsModal from "@/components/settings-modal"
import SubscriptionForm from "@/components/subscription-form"

interface Subscription {
  id: string
  name: string
  type: "total" | "monthly" | "yearly"
  price: number
  totalTime?: number
  category?: string
}

export default function AddPage() {
  const [showSettings, setShowSettings] = useState(false)
  const [settings, setSettings] = useState({
    workDaysPerMonth: 22,
    income: 0,
    incomeType: "monthly" as "yearly" | "monthly" | "daily",
    currency: "CNY"
  })

  // 加载设置
  useEffect(() => {
    const savedSettings = localStorage.getItem("settings")
    if (savedSettings) {
      const parsedSettings = JSON.parse(savedSettings)
      setSettings({
        workDaysPerMonth: parsedSettings.workDays || 22,
        income: parsedSettings.income || 0,
        incomeType: parsedSettings.incomeType || "monthly",
        currency: parsedSettings.currency || "CNY"
      })
    }
  }, [])

  // 保存设置
  const handleSaveSettings = (newSettings: {
    workDays: number
    income: number
    incomeType: "yearly" | "monthly" | "daily"
    currency: string
  }) => {
    setSettings({
      workDaysPerMonth: newSettings.workDays,
      income: newSettings.income,
      incomeType: newSettings.incomeType,
      currency: newSettings.currency
    })
    localStorage.setItem("settings", JSON.stringify(newSettings))
  }

  const handleSubmit = (data: Omit<Subscription, 'id'>) => {
    const newSubscription = {
      id: Date.now().toString(),
      ...data,
    }

    const existingSubscriptions = JSON.parse(localStorage.getItem("subscriptions") || "[]")
    const updatedSubscriptions = [...existingSubscriptions, newSubscription]
    localStorage.setItem("subscriptions", JSON.stringify(updatedSubscriptions))

    // 触发自定义事件通知其他页面数据已更新
    window.dispatchEvent(new CustomEvent('subscriptionsUpdated'))

    // 返回上一页，如果没有上一页则跳转到主页
    if (window.history.length > 1) {
      navigateBack()
    } else {
      navigateToHome()
    }
  }

  return (
    <div className="min-h-screen bg-background">
      {/* 统一头部导航 */}
      <AppHeader
        title="添加支出"
        subtitle="记录新的固定支出项目"
      />

      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-b from-blue-500/5 via-transparent to-purple-500/5 pointer-events-none" />
      <div className="absolute top-20 left-4 w-32 h-32 bg-gradient-to-br from-blue-400/10 to-transparent rounded-full blur-3xl pointer-events-none" />
      <div className="absolute top-40 right-6 w-24 h-24 bg-gradient-to-br from-purple-400/10 to-transparent rounded-full blur-2xl pointer-events-none" />

      <div className="breathing-space relative z-10">
        <SubscriptionForm
          onSubmit={handleSubmit}
          submitButtonText="添加固定支出"
          showQuickSelect={true}
        />
      </div>

      {/* 设置弹窗 */}
      <SettingsModal
        open={showSettings}
        onClose={() => setShowSettings(false)}
        workDaysPerMonth={settings.workDaysPerMonth}
        income={settings.income}
        incomeType={settings.incomeType}
        currency={settings.currency}
        onSave={handleSaveSettings}
      />

      {/* 底部导航栏 */}
      <BottomNavigation
        currentPage="add"
        onSettingsClick={() => setShowSettings(true)}
      />
    </div>
  )
}
