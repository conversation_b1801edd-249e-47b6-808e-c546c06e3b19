@tailwind base;
@tailwind components;
@tailwind utilities;

/* 隐藏滚动条但保持滚动功能 */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* 移动端安全区域适配 */
.safe-area-top {
  padding-top: 10px;
  padding-top: env(safe-area-inset-top, 10px);
}

/* Android WebView 特定适配 - 状态栏不覆盖内容 */
.android-webview .safe-area-top {
  padding-top: env(safe-area-inset-top, 0px);
}

/* 安卓设备上的玻璃头部样式调整 */
.android-webview .glass-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(30px);
  -webkit-backdrop-filter: blur(30px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  /* 状态栏不覆盖内容，不需要特殊边距 */
  margin-top: 0;
}

/* 确保 Android WebView 有正确的视口设置 */
@media (max-width: 640px) {
  .android-safe-area {
    padding-top: 24px;
    padding-bottom: 16px;
  }
}

/* Android WebView 特定修复 */
.android-webview body {
  padding-top: 0 !important;
  margin-top: 0 !important;
}

.android-webview .sticky.top-0 {
  top: 0 !important;
  margin-top: 0 !important;
}

/* Android WebView 修复渐变文字显示问题 */
.android-webview .bg-gradient-to-r.from-gray-900.to-gray-600.bg-clip-text.text-transparent {
  background: none !important;
  color: #1f2937 !important;
  -webkit-text-fill-color: #1f2937 !important;
}

.android-webview .bg-gradient-to-r.from-orange-400.to-yellow-500.bg-clip-text.text-transparent {
  background: none !important;
  color: #fb923c !important;
  -webkit-text-fill-color: #fb923c !important;
}

/* Android WebView 通用文字修复 */
.android-webview .bg-clip-text.text-transparent {
  background: none !important;
  color: inherit !important;
  -webkit-text-fill-color: currentColor !important;
}

/* Android WebView 禁用可能导致渲染问题的效果 */
.android-webview .glass-header,
.android-webview .glass-card {
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  background: rgba(255, 255, 255, 0.98) !important;
}

.android-webview .glass-button-white {
  backdrop-filter: none !important;
  -webkit-backdrop-filter: none !important;
  background: rgba(255, 255, 255, 0.9) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
}

/* Android WebView 文字颜色强制修复 */
.android-webview .text-foreground,
.android-webview .text-gray-900,
.android-webview .text-gray-600,
.android-webview h1,
.android-webview h2,
.android-webview h3,
.android-webview p {
  color: #1f2937 !important;
  -webkit-text-fill-color: #1f2937 !important;
}

.pt-safe {
  padding-top: env(safe-area-inset-top, 10px);
}

.pb-safe {
  padding-bottom: env(safe-area-inset-bottom, 20px);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom, 20px);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left, 0px);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right, 0px);
}

.safe-area-inset {
  padding: env(safe-area-inset-top, 10px) 
              env(safe-area-inset-right, 0px) 
              env(safe-area-inset-bottom, 20px) 
              env(safe-area-inset-left, 0px);
}

/* 适配 iOS Safari 底部安全区域 */
.safe-area-bottom-ios {
  padding-bottom: max(0px, env(safe-area-inset-bottom));
  /* 适配 iOS 15+ 的底部安全区域 */
  padding-bottom: max(0px, env(safe-area-inset-bottom, 20px));
}

/* 适配刘海屏 */
.notch-device {
  /* 留出状态栏空间 */
  padding-top: 44px;
  padding-top: max(44px, env(safe-area-inset-top));
}

/* 适配全面屏手势区域 */
.gesture-area {
  /* 底部手势区域适配 */
  padding-bottom: max(0px, env(safe-area-inset-bottom));
  min-height: calc(100vh - env(safe-area-inset-top));
}

/* 移动端优化 */
.mobile-optimized {
  /* 触摸目标大小至少 44px */
  min-height: 44px;
  min-width: 44px;
}

/* 防止 iOS Safari 弹跳 */
.ios-no-bounce {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: none;
}

/* 适配 iPhone X 及更新机型 */
@supports (padding: max(0px)) {
  .iphone-x-safe {
    padding-top: max(44px, env(safe-area-inset-top));
    padding-bottom: max(34px, env(safe-area-inset-bottom));
  }
}

/* 适配动态岛 */
@supports (padding: max(0px)) {
  .dynamic-island-safe {
    padding-top: max(59px, env(safe-area-inset-top));
    padding-bottom: max(34px, env(safe-area-inset-bottom));
  }
}

/* 移动端滚动优化 */
.mobile-scroll {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* 防止双击缩放 */
.prevent-zoom {
  touch-action: manipulation;
}

/* 移动端按钮优化 */
.mobile-button {
  min-height: 44px;
  min-width: 44px;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 500;
}

/* 卡片切换动画 */
@keyframes slide-in-left {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-right {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-out-left {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

@keyframes slide-out-right {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fade-out {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.95);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes scale-out {
  from {
    opacity: 1;
    transform: scale(1);
  }
  to {
    opacity: 0;
    transform: scale(0.8);
  }
}

@keyframes flip-in-left {
  from {
    transform: rotateY(90deg);
    opacity: 0;
  }
  to {
    transform: rotateY(0deg);
    opacity: 1;
  }
}

@keyframes flip-in-right {
  from {
    transform: rotateY(-90deg);
    opacity: 0;
  }
  to {
    transform: rotateY(0deg);
    opacity: 1;
  }
}

@keyframes flip-out-left {
  from {
    transform: rotateY(0deg);
    opacity: 1;
  }
  to {
    transform: rotateY(-90deg);
    opacity: 0;
  }
}

@keyframes flip-out-right {
  from {
    transform: rotateY(0deg);
    opacity: 1;
  }
  to {
    transform: rotateY(90deg);
    opacity: 0;
  }
}

.animate-slide-in-left {
  animation: slide-in-left 0.3s ease-in-out forwards;
}

.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-in-out forwards;
}

.animate-slide-out-left {
  animation: slide-out-left 0.3s ease-in-out forwards;
}

.animate-slide-out-right {
  animation: slide-out-right 0.3s ease-in-out forwards;
}

.animate-fade-in {
  animation: fade-in 0.3s ease-in-out forwards;
}

.animate-fade-out {
  animation: fade-out 0.3s ease-in-out forwards;
}

.animate-scale-in {
  animation: scale-in 0.3s ease-in-out forwards;
}

.animate-scale-out {
  animation: scale-out 0.3s ease-in-out forwards;
}

.animate-flip-in-left {
  animation: flip-in-left 0.3s ease-in-out forwards;
}

.animate-flip-in-right {
  animation: flip-in-right 0.3s ease-in-out forwards;
}

.animate-flip-out-left {
  animation: flip-out-left 0.3s ease-in-out forwards;
}

.animate-flip-out-right {
  animation: flip-out-right 0.3s ease-in-out forwards;
}

:root {
  --background: 210 20% 91%;
  --foreground: 210 25% 25%;
  --card: 0 0% 100% / 0.7;
  --card-foreground: 210 25% 25%;
  --popover: 0 0% 100% / 0.9;
  --popover-foreground: 210 25% 25%;
  --primary: 206 73% 66%;
  --primary-foreground: 0 0% 100%;
  --secondary: 0 0% 100% / 0.5;
  --secondary-foreground: 210 25% 25%;
  --muted: 0 0% 100% / 0.3;
  --muted-foreground: 215 16% 47%;
  --accent: 206 73% 66%;
  --accent-foreground: 0 0% 100%;
  --destructive: 0 69% 75%;
  --destructive-foreground: 0 0% 100%;
  --border: 0 0% 100% / 0.2;
  --input: 0 0% 100% / 0.6;
  --ring: 206 73% 66%;
  --warning: 0 69% 75%;
  --radius: 1rem;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-warning: var(--warning);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
    font-family: -apple-system, BlinkMacSystemFont, "SF Pro Rounded", system-ui, sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* 玻璃拟态效果 */
.glass-card {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.glass-header {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(30px);
  -webkit-backdrop-filter: blur(30px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-button {
  background: rgba(99, 179, 237, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(99, 179, 237, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-button:hover {
  background: rgba(99, 179, 237, 1);
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(99, 179, 237, 0.4);
}

/* 素白毛玻璃按钮样式 */
.glass-button-white {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  color: rgba(45, 55, 72, 0.8);
}

.glass-button-white:hover {
  background: rgba(255, 255, 255, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  color: rgba(45, 55, 72, 1);
}

.glass-button-white:active {
  transform: translateY(0);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 重力弹跳动画 */
@keyframes bounce-spring {
  0% {
    transform: scale(1) translateY(0);
  }
  20% {
    transform: scale(1.05) translateY(-4px);
  }
  40% {
    transform: scale(0.98) translateY(2px);
  }
  60% {
    transform: scale(1.02) translateY(-1px);
  }
  80% {
    transform: scale(0.99) translateY(0.5px);
  }
  100% {
    transform: scale(1) translateY(0);
  }
}

.bounce-spring {
  animation: bounce-spring 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 数值变化动画 */
@keyframes number-pop {
  0% {
    transform: scale(1);
    color: #2d3748;
  }
  50% {
    transform: scale(1.1);
    color: #fc8181;
  }
  100% {
    transform: scale(1);
    color: #fc8181;
  }
}

.number-change {
  animation: number-pop 0.5s ease-out;
}

/* 呼吸动画 */
@keyframes breathe {
  0%,
  100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

.breathe {
  animation: breathe 3s ease-in-out infinite;
}

/* 负担等级动画 */
/* 最小负担 - 轻柔呼吸 */
@keyframes burden-minimal {
  0%, 100% {
    opacity: 0.85;
    transform: scale(1);
    box-shadow: 0 8px 32px rgba(99, 179, 237, 0.08), 0 4px 16px rgba(255, 255, 255, 0.05);
  }
  50% {
    opacity: 1;
    transform: scale(1.008);
    box-shadow: 0 12px 48px rgba(99, 179, 237, 0.12), 0 6px 24px rgba(255, 255, 255, 0.08);
  }
}

.burden-minimal {
  animation: burden-minimal 4.5s ease-in-out infinite;
}

/* 轻负担 - 平缓波动 */
@keyframes burden-light {
  0%, 100% {
    opacity: 0.75;
    transform: scale(1);
    box-shadow: 0 10px 40px rgba(59, 130, 246, 0.15);
  }
  50% {
    opacity: 1;
    transform: scale(1.015);
    box-shadow: 0 20px 60px rgba(59, 130, 246, 0.2);
  }
}

.burden-light {
  animation: burden-light 3.5s ease-in-out infinite;
}

/* 中等负担 - 明显呼吸 */
@keyframes burden-moderate {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
    box-shadow: 0 15px 50px rgba(234, 179, 8, 0.2);
  }
  50% {
    opacity: 1;
    transform: scale(1.025);
    box-shadow: 0 25px 70px rgba(234, 179, 8, 0.25);
  }
}

.burden-moderate {
  animation: burden-moderate 3s ease-in-out infinite;
}

/* 重负担 - 沉重呼吸 */
@keyframes burden-heavy {
  0%, 100% {
    opacity: 0.65;
    transform: scale(1);
    box-shadow: 0 20px 60px rgba(249, 115, 22, 0.25);
  }
  50% {
    opacity: 1;
    transform: scale(1.03);
    box-shadow: 0 30px 80px rgba(249, 115, 22, 0.3);
  }
}

.burden-heavy {
  animation: burden-heavy 2.5s ease-in-out infinite;
}

/* 极重负担 - 急促颤动 */
@keyframes burden-extreme {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1) rotate(0deg);
    box-shadow: 0 25px 70px rgba(239, 68, 68, 0.3);
  }
  25% {
    opacity: 0.9;
    transform: scale(1.02) rotate(0.5deg);
    box-shadow: 0 35px 90px rgba(239, 68, 68, 0.4);
  }
  50% {
    opacity: 1;
    transform: scale(1.035) rotate(0deg);
    box-shadow: 0 40px 100px rgba(239, 68, 68, 0.45);
  }
  75% {
    opacity: 0.9;
    transform: scale(1.02) rotate(-0.5deg);
    box-shadow: 0 35px 90px rgba(239, 68, 68, 0.4);
  }
}

.burden-extreme {
  animation: burden-extreme 2s ease-in-out infinite;
}

/* 手势交互样式 */
.swipe-indicator {
  position: relative;
  overflow: hidden;
}

.swipe-indicator::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.swipe-indicator.swiping::after {
  left: 100%;
}

/* 长按效果 */
.long-press {
  transition: all 0.2s ease;
}

.long-press:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.9);
}

/* 移动端优化 */
@media (max-width: 640px) {
  body {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    overscroll-behavior: none;
  }

  input,
  textarea {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }
}

/* 空间留白系统 */
.breathing-space {
  padding: 2rem 1.5rem;
}

.dramatic-number {
  font-weight: 200;
  letter-spacing: -0.02em;
  line-height: 0.9;
}
