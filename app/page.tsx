"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { Plus, TrendingUp } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import SettingsModal from "@/components/settings-modal"
import ButtonGroup from "@/components/ui/button-group"
import SwipeHint from "@/components/swipe-hint"
import AnimatedViewContainer from "@/components/animated-view-container"
import MobileLink from "@/components/mobile-link"
import AppHeader from "@/components/app-header"
import BottomNavigation from "@/components/bottom-navigation"
import { timeLabels } from "@/lib/time-config"
import { useTimeView } from "@/contexts/time-view-context"
import { usePageSwipe } from "@/hooks/use-page-swipe"

interface Subscription {
  id: string
  name: string
  type: "total" | "monthly" | "yearly"
  price: number
  totalTime?: number
  category?: string
  startDate?: string
  endDate?: string
}

interface SettingsData {
  workDays: number
  income: number
  incomeType: "yearly" | "monthly" | "daily"
  currency: string
}

export default function HomePage() {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([])
  const [workDaysPerMonth, setWorkDaysPerMonth] = useState(22)
  const [income, setIncome] = useState(0)
  const [incomeType, setIncomeType] = useState<"yearly" | "monthly" | "daily">("monthly")
  const [currency, setCurrency] = useState("CNY")
  const [showSettings, setShowSettings] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  const { timeView, setTimeView } = useTimeView()

  // 添加页面滑动功能
  usePageSwipe({
    currentTimeView: timeView,
    onTimeViewChange: setTimeView,
    enabled: !isLoading && subscriptions.length > 0 // 只有在数据加载完成且有订阅数据时才启用滑动
  })

  const [isRefreshing, setIsRefreshing] = useState(false)
  const [lastTotal, setLastTotal] = useState(0)
  const totalRef = useRef<HTMLDivElement>(null)
  const startY = useRef(0)
  const currentY = useRef(0)

  // 货币符号映射
  const getCurrencySymbol = (currencyCode: string) => {
    const currencyMap: { [key: string]: string } = {
      CNY: "¥",
      USD: "$",
      EUR: "€",
      GBP: "£",
      JPY: "¥",
      KRW: "₩",
      HKD: "HK$",
      TWD: "NT$",
    }
    return currencyMap[currencyCode] || "¥"
  }

  useEffect(() => {
    const loadData = () => {
      // Load subscriptions
      const savedSubscriptions = localStorage.getItem("subscriptions")
      if (savedSubscriptions) {
        setSubscriptions(JSON.parse(savedSubscriptions))
      }
      // Load settings
      const savedSettings = localStorage.getItem("settings")
      if (savedSettings) {
        const { workDays, income, incomeType, currency } = JSON.parse(savedSettings)
        setWorkDaysPerMonth(workDays || 22)
        setIncome(income || 0)
        setIncomeType(incomeType || "monthly")
        setCurrency(currency || "CNY")
      }
      // 数据加载完成
      setIsLoading(false)
    }

    // 初始加载
    loadData()

    // 监听storage变化（跨标签页）
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'subscriptions' || e.key === 'settings') {
        loadData()
      }
    }

    // 监听页面可见性变化（移动端返回时刷新）
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        loadData()
      }
    }

    // 监听自定义事件（同页面内数据更新）
    const handleDataUpdate = () => {
      loadData()
    }

    window.addEventListener('storage', handleStorageChange)
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('subscriptionsUpdated', handleDataUpdate)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('subscriptionsUpdated', handleDataUpdate)
    }
  }, [])

  const handleSaveSettings = (settings: SettingsData) => {
    setWorkDaysPerMonth(settings.workDays)
    setIncome(settings.income)
    setIncomeType(settings.incomeType)
    setCurrency(settings.currency)
    localStorage.setItem("settings", JSON.stringify(settings))
  }

  // 计算显示成本 - 直接基于原始价格计算，避免精度损失
  const calculateDisplayCost = (sub: Subscription, targetTimeView: string): number => {
    switch (targetTimeView) {
      case "daily":
        // 计算工作日成本
        switch (sub.type) {
          case "total":
            if (sub.totalTime) {
              return sub.price / (sub.totalTime * workDaysPerMonth)
            }
            return 0
          case "monthly":
            return sub.price / workDaysPerMonth
          case "yearly":
            return sub.price / (workDaysPerMonth * 12)
          default:
            return 0
        }
      case "weekly":
        // 假设每周5个工作日
        return calculateDisplayCost(sub, "daily") * 5
      case "monthly":
        // 直接返回月成本
        switch (sub.type) {
          case "total":
            if (sub.totalTime) {
              return sub.price / sub.totalTime
            }
            return 0
          case "monthly":
            return sub.price
          case "yearly":
            return sub.price / 12
          default:
            return 0
        }
      case "yearly":
        // 直接返回年成本
        switch (sub.type) {
          case "total":
            if (sub.totalTime) {
              return sub.price / sub.totalTime * 12
            }
            return 0
          case "monthly":
            return sub.price * 12
          case "yearly":
            return sub.price
          default:
            return 0
        }
      default:
        return 0
    }
  }

  // 计算总成本
  const getTotalCost = () => {
    return subscriptions.reduce((sum, sub) => sum + calculateDisplayCost(sub, timeView), 0)
  }

  // Calculate burden ratio
  const calculateBurdenRatio = () => {
    if (!income || income <= 0) return null

    const yearlySubscriptionCost = subscriptions.reduce((sum, sub) => {
      return sum + calculateDisplayCost(sub, "yearly")
    }, 0)

    let yearlyIncome = 0
    switch (incomeType) {
      case "daily":
        // 使用实际设置的工作天数计算年收入
        yearlyIncome = income * workDaysPerMonth * 12
        break
      case "monthly":
        yearlyIncome = income * 12
        break
      case "yearly":
        yearlyIncome = income
        break
    }

    if (yearlyIncome <= 0) return null

    return (yearlySubscriptionCost / yearlyIncome) * 100
  }

  // 格式化数字显示
  const formatNumber = (num: number) => {
    if (num >= 10000) {
      return {
        value: (num / 10000).toFixed(1),
        unit: "万",
        size: "text-6xl", // 较小字体
      }
    } else if (num >= 1000) {
      return {
        value: num.toLocaleString("zh-CN", { maximumFractionDigits: 0 }),
        unit: "",
        size: "text-5xl", // 中等字体
      }
    } else {
      return {
        value: num.toFixed(2),
        unit: "",
        size: "text-7xl", // 最大字体
      }
    }
  }

  // 数值变化动画
  useEffect(() => {
    const currentTotal = getTotalCost()
    if (currentTotal !== lastTotal && lastTotal !== 0) {
      totalRef.current?.classList.add("number-change")
      setTimeout(() => {
        totalRef.current?.classList.remove("number-change")
      }, 500)
    }
    setLastTotal(currentTotal)
  }, [subscriptions, timeView, workDaysPerMonth, income, incomeType])

  // 排序固定支出项目 - 按当前时间视图的成本排序
  const sortedSubscriptions = [...subscriptions].sort((a, b) => calculateDisplayCost(b, timeView) - calculateDisplayCost(a, timeView))





  // 下拉刷新
  const handleTouchStart = (e: React.TouchEvent) => {
    startY.current = e.touches[0].clientY
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    currentY.current = e.touches[0].clientY
    const diff = currentY.current - startY.current

    if (diff > 100 && window.scrollY === 0) {
      setIsRefreshing(true)
    }
  }

  const handleTouchEnd = () => {
    if (isRefreshing) {
      setTimeout(() => {
        setIsRefreshing(false)
      }, 1000)
    }
  }



  const totalCost = getTotalCost()
  const formattedNumber = formatNumber(totalCost)
  const burdenRatio = calculateBurdenRatio()

  // 根据负担程度计算动态效果等级
  const getBurdenLevel = () => {
    if (!burdenRatio) return 'normal'

    if (burdenRatio >= 80) return 'extreme'      // 极重负担 >= 80%
    if (burdenRatio >= 60) return 'heavy'        // 重负担 >= 60%
    if (burdenRatio >= 40) return 'moderate'     // 中等负担 >= 40%
    if (burdenRatio >= 20) return 'light'        // 轻负担 >= 20%
    return 'minimal'                             // 最小负担 < 20%
  }

  const burdenLevel = getBurdenLevel()

  // 根据负担等级获取动画类名和颜色
  const getBurdenStyles = () => {
    switch (burdenLevel) {
      case 'extreme':
        return {
          cardClass: 'burden-extreme',
          textColor: 'text-red-400',
          glowColor: 'shadow-red-500/30',
          description: '极重负担'
        }
      case 'heavy':
        return {
          cardClass: 'burden-heavy',
          textColor: 'text-orange-400',
          glowColor: 'shadow-orange-500/20',
          description: '重负担'
        }
      case 'moderate':
        return {
          cardClass: 'burden-moderate',
          textColor: 'text-yellow-400',
          glowColor: 'shadow-yellow-500/15',
          description: '中等负担'
        }
      case 'light':
        return {
          cardClass: 'burden-light',
          textColor: 'text-blue-400',
          glowColor: 'shadow-blue-500/10',
          description: '轻负担'
        }
      case 'minimal':
        return {
          cardClass: 'burden-minimal',
          textColor: 'text-blue-300',
          glowColor: 'shadow-blue-400/10',
          description: '轻松负担'
        }
      default:
        return {
          cardClass: 'breathe',
          textColor: 'text-warning',
          glowColor: 'shadow-xl',
          description: ''
        }
    }
  }

  const burdenStyles = getBurdenStyles()

  return (
    <div
      className="min-h-screen bg-background gesture-area"
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
    >
      {/* 下拉刷新指示器 */}
      {isRefreshing && (
        <div className="fixed top-0 left-0 right-0 z-50 flex justify-center pt-4">
          <div className="glass-card px-4 py-2 rounded-full">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
              <span className="text-sm text-foreground">刷新中...</span>
            </div>
          </div>
        </div>
      )}

      {/* 统一头部导航 */}
      <AppHeader
        title="NoOver"
        subtitle={!isLoading && subscriptions.length > 0 ? (
          `${new Set(subscriptions.map(sub => sub.category || "其他")).size} 个分类 · ${subscriptions.length} 项支出`
        ) : (
          "开始记录你的固定支出"
        )}
      />

      {/* 时间视图选择器 - 只在有固定支出时显示 */}
      {!isLoading && subscriptions.length > 0 && (
        <div className="bg-white/5 backdrop-blur-sm border-b border-white/5 py-4">
          <div className="mx-auto px-6 max-w-7xl">
          <ButtonGroup
            options={[
              { value: "daily", label: timeLabels.daily },
              { value: "weekly", label: timeLabels.weekly },
              { value: "monthly", label: timeLabels.monthly },
              { value: "yearly", label: timeLabels.yearly },
            ]}
            value={timeView}
            onChange={(value) => setTimeView(value as typeof timeView)}
          />
          </div>
        </div>
      )}

      {/* 主要数据展示区域 - 只在有固定支出时显示 */}
      {!isLoading && subscriptions.length > 0 && (
        <div className="py-8 bg-gradient-to-b from-white/5 to-transparent">
          <div className="mx-auto px-6 max-w-7xl">
          <AnimatedViewContainer currentTimeView={timeView} animationType="slide">
            <MobileLink href="/ranking" className="block">
              <div className={`glass-card rounded-3xl p-8 ${burdenStyles.cardClass} ${burdenStyles.glowColor} border border-white/20 cursor-pointer hover:scale-[1.02] transition-transform duration-200`}>
            <div className="text-center space-y-4">
              {/* 负担等级指示器 */}
              {burdenLevel !== 'normal' && (
                <div className="mb-4">
                  <div className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${burdenStyles.textColor} bg-white/10 border border-current/20`}>
                    <div className={`w-2 h-2 rounded-full ${burdenStyles.textColor.replace('text-', 'bg-')} mr-2 animate-pulse`}></div>
                    {burdenStyles.description}
                  </div>
                </div>
              )}

              {/* 主要数值 */}
              <div className="space-y-3">
                <div ref={totalRef} className="flex items-baseline justify-center space-x-2">
                  <span className={`text-2xl font-light ${burdenStyles.textColor}`}>{getCurrencySymbol(currency)}</span>
                  <span className={`dramatic-number ${formattedNumber.size} font-light ${burdenStyles.textColor} leading-none`}>
                    {formattedNumber.value}
                  </span>
                  {formattedNumber.unit && (
                    <span className={`text-3xl font-light ${burdenStyles.textColor}`}>{formattedNumber.unit}</span>
                  )}
                </div>
                <div className="text-muted-foreground text-xl font-light">{timeLabels[timeView]}负担总额</div>

                {/* 精确数值（小字显示） */}
                {formattedNumber.unit && (
                  <div className="text-xs text-muted-foreground/70 mt-2">
                    精确值：{getCurrencySymbol(currency)}{totalCost.toLocaleString("zh-CN", { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                  </div>
                )}
              </div>

              {/* 收入占比 */}
              {burdenRatio !== null && (
                <div className="pt-4">
                  <div className="w-full bg-white/20 rounded-full h-3 relative overflow-hidden">
                    {/* 背景纯色 - 显示总长度 */}
                    <div className="absolute inset-0 bg-gray-400/25 rounded-full"></div>
                    {/* 前景条 - 根据负担程度动态变色 */}
                    <div
                      className={`h-3 rounded-full transition-all duration-700 ease-out ${
                        burdenLevel === 'extreme' ? 'bg-gradient-to-r from-red-500 to-red-600' :
                        burdenLevel === 'heavy' ? 'bg-gradient-to-r from-orange-500 to-red-500' :
                        burdenLevel === 'moderate' ? 'bg-gradient-to-r from-yellow-500 to-orange-500' :
                        burdenLevel === 'light' ? 'bg-gradient-to-r from-blue-500 to-yellow-500' :
                        burdenLevel === 'minimal' ? 'bg-gradient-to-r from-green-500 to-blue-500' :
                        'bg-gradient-to-r from-primary to-warning'
                      }`}
                      style={{ width: `${Math.min(burdenRatio, 100)}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between items-center text-xs text-muted-foreground mt-2">
                    <div className="flex items-center">
                      <span>收入占比</span>
                    </div>
                    <span className={`font-medium ${burdenStyles.textColor}`}>{burdenRatio.toFixed(2)}%</span>
                  </div>
                </div>
              )}
            </div>
          </div>
          </MobileLink>
          </AnimatedViewContainer>
          </div>
        </div>
      )}

      {/* 固定支出列表区域 */}
      <div className="pb-32">
        <div className="mx-auto px-6 max-w-7xl">
        {!isLoading && sortedSubscriptions.length === 0 ? (
          <div className="flex items-center justify-center min-h-[60vh]">
            <div className="text-center">
              <div className="glass-card rounded-3xl p-12 space-y-8 border border-white/20 shadow-xl max-w-md mx-auto">
                <div className="w-32 h-32 mx-auto bg-gradient-to-br from-primary/20 to-warning/20 rounded-full flex items-center justify-center shadow-lg">
                  <TrendingUp className="h-16 w-16 text-primary" />
                </div>
                <div className="space-y-6">
                  <h3 className="text-3xl font-medium text-foreground">开始记</h3>
                  <p className="text-muted-foreground leading-relaxed text-xl">
                    现代人身上的担子有多重？
                    <br />
                    让数字告诉你真相
                  </p>
                </div>
                <div className="pt-6">
                  <MobileLink href="/add">
                    <Button className="glass-button h-16 px-12 text-xl font-medium rounded-full shadow-lg hover:scale-105 transition-transform">
                      <Plus className="h-7 w-7 mr-4" />
                      添加第一个固定支出
                    </Button>
                  </MobileLink>
                </div>
              </div>
            </div>
          </div>
        ) : null}
        </div>
      </div>

      {/* 设置模态框 */}
      <SettingsModal
        open={showSettings}
        onClose={() => setShowSettings(false)}
        workDaysPerMonth={workDaysPerMonth}
        income={income}
        incomeType={incomeType}
        currency={currency}
        onSave={handleSaveSettings}
      />

      {/* 滑动提示 */}
      <SwipeHint show={!isLoading && subscriptions.length > 0} currentTimeView={timeView} />

      {/* 底部导航栏 */}
      <BottomNavigation
        currentPage="home"
        onSettingsClick={() => setShowSettings(true)}
      />

    </div>
  )
}
