"use client"

import { useEffect, useState, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import AppHeader from "@/components/app-header"
import BottomNavigation from "@/components/bottom-navigation"
import ClickableBar<PERSON>hart from "@/components/clickable-bar-chart"
import EditSubscriptionModal from "@/components/edit-subscription-modal"
import SettingsModal from "@/components/settings-modal"
import ButtonGroup from "@/components/ui/button-group"
import SwipeHint from "@/components/swipe-hint"
import AnimatedViewContainer from "@/components/animated-view-container"
import { timeLabels, timeUnits } from "@/lib/time-config"
import { useTimeView } from "@/contexts/time-view-context"
import { usePageSwipe } from "@/hooks/use-page-swipe"

interface Subscription {
  id: string
  name: string
  type: "total" | "monthly" | "yearly"
  price: number
  category?: string
  totalTime?: number
}

interface SettingsData {
  currency: string
  income: number
  incomeType: "daily" | "monthly" | "yearly"
  workDays: number
}

function RankingContent() {
  const searchParams = useSearchParams()
  const categoryParam = searchParams.get("category")
  
  // 获取分类名称：优先使用查询参数，其次使用 localStorage，最后使用"全部"
  const getCategoryName = () => {
    console.log('getCategoryName 被调用')
    console.log('查询参数 categoryParam:', categoryParam)
    
    if (categoryParam) {
      const decodedCategory = decodeURIComponent(categoryParam)
      console.log('使用查询参数:', decodedCategory)
      return decodedCategory
    }
    
    // 在移动端，从 localStorage 读取分类参数
    if (typeof window !== 'undefined') {
      const storedCategory = localStorage.getItem('selectedCategory')
      console.log('localStorage 中的分类:', storedCategory)
      if (storedCategory) {
        console.log('使用 localStorage 中的分类:', storedCategory)
        return storedCategory
      }
    }
    
    console.log('使用默认值: 全部')
    return "全部"
  }
  
  const [categoryName, setCategoryName] = useState<string>("全部")
  const [categoryProcessed, setCategoryProcessed] = useState(false)
  
  // 初始化分类名称
  useEffect(() => {
    const name = getCategoryName()
    console.log('当前分类名称:', name)
    setCategoryName(name)
    setCategoryProcessed(true)
  }, [categoryParam])
  
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([])
  const [settings, setSettings] = useState<SettingsData>({
    currency: "CNY",
    income: 0,
    incomeType: "monthly",
    workDays: 22
  })
  const { timeView, setTimeView } = useTimeView()
  const [editingSubscription, setEditingSubscription] = useState<Subscription | null>(null)
  const [showSettings, setShowSettings] = useState(false)

  // 添加页面滑动功能
  usePageSwipe({
    currentTimeView: timeView,
    onTimeViewChange: setTimeView,
    enabled: subscriptions.length > 0 // 只有在有订阅数据时才启用滑动
  })

  // 处理点击订阅项目
  const handleItemClick = (subscription: Subscription) => {
    setEditingSubscription(subscription)
  }

  // 加载数据
  useEffect(() => {
    const loadData = () => {
      const savedSubscriptions = localStorage.getItem("subscriptions")
      const savedSettings = localStorage.getItem("settings")
      
      if (savedSubscriptions) {
        const allSubs = JSON.parse(savedSubscriptions)
        // 如果是"全部"，显示所有订阅，否则按分类筛选
        const filteredSubs = categoryName === "全部" 
          ? allSubs 
          : allSubs.filter((sub: Subscription) => (sub.category || "其他") === categoryName)
        setSubscriptions(filteredSubs)
      }
      
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings))
      }
      
      // 在移动端，分类处理完成后清除 localStorage 中的分类信息
      if (categoryProcessed && typeof window !== 'undefined' && localStorage.getItem('selectedCategory')) {
        localStorage.removeItem('selectedCategory')
        console.log('已清除 localStorage 中的分类信息')
      }
    }

    // 初始加载
    loadData()

    // 监听storage变化（跨标签页）
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'subscriptions' || e.key === 'settings') {
        loadData()
      }
    }

    // 监听页面可见性变化（移动端返回时刷新）
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        loadData()
      }
    }

    // 监听自定义事件（同页面内数据更新）
    const handleDataUpdate = () => {
      loadData()
    }

    window.addEventListener('storage', handleStorageChange)
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('subscriptionsUpdated', handleDataUpdate)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('subscriptionsUpdated', handleDataUpdate)
    }
  }, [categoryName, categoryProcessed]) // 当分类名称变化时重新加载数据

  // 货币符号映射
  const getCurrencySymbol = (currencyCode: string) => {
    const currencyMap: { [key: string]: string } = {
      CNY: "¥", USD: "$", EUR: "€", GBP: "£", JPY: "¥", KRW: "₩", HKD: "HK$", TWD: "NT$",
    }
    return currencyMap[currencyCode] || "¥"
  }

  // 计算显示成本
  const calculateDisplayCost = (sub: Subscription, targetTimeView: string): number => {
    switch (targetTimeView) {
      case "daily":
        switch (sub.type) {
          case "total":
            if (sub.totalTime) {
              return sub.price / (sub.totalTime * settings.workDays)
            }
            return 0
          case "monthly":
            return sub.price / settings.workDays
          case "yearly":
            return sub.price / (settings.workDays * 12)
          default:
            return 0
        }
      case "weekly":
        return calculateDisplayCost(sub, "daily") * 5
      case "monthly":
        switch (sub.type) {
          case "total":
            if (sub.totalTime) {
              return sub.price / sub.totalTime
            }
            return 0
          case "monthly":
            return sub.price
          case "yearly":
            return sub.price / 12
          default:
            return 0
        }
      case "yearly":
        switch (sub.type) {
          case "total":
            if (sub.totalTime) {
              return sub.price / sub.totalTime * 12
            }
            return 0
          case "monthly":
            return sub.price * 12
          case "yearly":
            return sub.price
          default:
            return 0
        }
      default:
        return 0
    }
  }

  // 保存订阅
  const saveSubscription = (updatedSubscription: Subscription) => {
    const allSubscriptions = JSON.parse(localStorage.getItem("subscriptions") || "[]")
    const updatedAll = allSubscriptions.map((sub: Subscription) =>
      sub.id === updatedSubscription.id ? updatedSubscription : sub
    )
    localStorage.setItem("subscriptions", JSON.stringify(updatedAll))

    // 触发自定义事件通知其他页面数据已更新
    window.dispatchEvent(new CustomEvent('subscriptionsUpdated'))

    // 更新当前页面的订阅列表
    const filteredSubs = categoryName === "全部"
      ? updatedAll
      : updatedAll.filter((sub: Subscription) => (sub.category || "其他") === categoryName)
    setSubscriptions(filteredSubs)
    setEditingSubscription(null)
  }

  // 删除订阅
  const removeSubscription = (id: string) => {
    const allSubscriptions = JSON.parse(localStorage.getItem("subscriptions") || "[]")
    const updatedAll = allSubscriptions.filter((sub: Subscription) => sub.id !== id)
    localStorage.setItem("subscriptions", JSON.stringify(updatedAll))

    // 触发自定义事件通知其他页面数据已更新
    window.dispatchEvent(new CustomEvent('subscriptionsUpdated'))

    // 更新当前页面的订阅列表
    const filteredSubs = categoryName === "全部"
      ? updatedAll
      : updatedAll.filter((sub: Subscription) => (sub.category || "其他") === categoryName)
    setSubscriptions(filteredSubs)
  }

  // 按当前时间视图排序
  const sortedSubscriptions = [...subscriptions].sort((a, b) =>
    calculateDisplayCost(b, timeView) - calculateDisplayCost(a, timeView)
  )

  const handleSaveSettings = (newSettings: SettingsData) => {
    setSettings(newSettings)
    localStorage.setItem("settings", JSON.stringify(newSettings))
  }

  return (
    <div className="min-h-screen bg-background">
      {/* 统一头部导航 */}
      <AppHeader
        title="详细排名"
        subtitle={subscriptions.length > 0 ? (
          `${categoryName === "全部" ? "所有" : categoryName} · ${subscriptions.length} 项支出`
        ) : (
          "暂无支出项目"
        )}
      />

      {/* 时间视图选择器 - 只在有固定支出时显示 */}
      {subscriptions.length > 0 && (
        <div className="bg-white/5 backdrop-blur-sm border-b border-white/5 px-6 py-4">
          <ButtonGroup
            options={[
              { value: "daily", label: timeLabels.daily },
              { value: "weekly", label: timeLabels.weekly },
              { value: "monthly", label: timeLabels.monthly },
              { value: "yearly", label: timeLabels.yearly },
            ]}
            value={timeView}
            onChange={(value) => setTimeView(value as any)}
          />
        </div>
      )}

      <div className="breathing-space">
        {/* 条形图展示 */}
        <AnimatedViewContainer currentTimeView={timeView} animationType="slide">
          {subscriptions.length > 0 ? (
            <ClickableBarChart
              subscriptions={sortedSubscriptions}
              displayCosts={sortedSubscriptions.map(sub => calculateDisplayCost(sub, timeView))}
              currencySymbol={getCurrencySymbol(settings.currency)}
              timeUnit={timeUnits[timeView]}
              onItemClick={handleItemClick}
              onItemDelete={removeSubscription}
            />
          ) : (
            <div className="glass-card p-8 rounded-2xl border border-white/20 text-center">
              <p className="text-muted-foreground">该分类下暂无固定支出</p>
            </div>
          )}
        </AnimatedViewContainer>
      </div>

      {/* 编辑订阅模态框 */}
      {editingSubscription && (
        <EditSubscriptionModal
          subscription={editingSubscription}
          onSave={saveSubscription}
          onClose={() => setEditingSubscription(null)}
        />
      )}

      {/* 设置模态框 */}
      <SettingsModal
        open={showSettings}
        onClose={() => setShowSettings(false)}
        workDaysPerMonth={settings.workDays}
        income={settings.income}
        incomeType={settings.incomeType}
        currency={settings.currency}
        onSave={handleSaveSettings}
      />

      {/* 滑动提示 */}
      <SwipeHint show={subscriptions.length > 0} currentTimeView={timeView} />

      {/* 底部导航栏 */}
      <BottomNavigation
        currentPage="ranking"
        onSettingsClick={() => setShowSettings(true)}
      />
    </div>
  )
}

export default function RankingPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <RankingContent />
    </Suspense>
  )
}