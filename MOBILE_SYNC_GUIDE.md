# 移动端开发同步流程

每次修改源码后，需要按照以下步骤同步到 Xcode 进行调试：

## 同步步骤

### 1. 修改源码
- 修改 Next.js 源代码文件
- 修改 CSS 样式文件
- 修改配置文件等

### 2. 构建项目
```bash
npm run build
```
- 将 Next.js 项目构建为静态文件
- 输出到 `out` 目录

### 3. 同步到移动端项目
```bash
npm run mobile:sync
```
- 将构建文件复制到 iOS 和 Android 项目
- 更新原生插件配置
- 同步资源文件到 `ios/App/App/public` 和 `android/app/src/main/assets/public`

### 4. 打开 Xcode（如果未打开）
```bash
npm run mobile:open:ios
```
- 打开 iOS 项目的 Xcode 工作空间
- 文件路径：`ios/App/App.xcworkspace`

### 5. 在 Xcode 中运行
- 选择目标设备（模拟器或真机）
- 点击运行按钮（▶️）或按 `Cmd + R`
- 应用会启动并显示最新修改

## 快速同步命令

如果 Xcode 已经打开，只需要执行前两步：

```bash
# 构建并同步
npm run build && npm run mobile:sync
```

## 注意事项

1. **必须执行构建步骤** - 直接修改源码不会自动同步到移动端
2. **同步时间** - 构建和同步过程通常需要 10-30 秒
3. **热重载** - 在移动端调试时，热重载功能有限，建议完整构建测试
4. **清理缓存** - 如果遇到奇怪问题，可以尝试清理项目：
   ```bash
   # 清理 Next.js 缓存
   rm -rf .next
   rm -rf out
   
   # 清理 iOS 缓存（在 Xcode 中）
   # Product -> Clean Build Folder
   ```

## 常用命令列表

| 命令 | 功能 |
|------|------|
| `npm run build` | 构建生产版本 |
| `npm run mobile:sync` | 同步到移动端项目 |
| `npm run mobile:open:ios` | 打开 iOS 项目 |
| `npm run mobile:open` | 打开 Android 项目 |
| `npm run mobile:ios` | 直接运行 iOS 应用 |
| `npm run mobile:android` | 直接运行 Android 应用 |

## 开发建议

1. **开发阶段**：使用 `npm run dev` 进行 Web 端开发和调试
2. **移动端测试**：完成功能开发后，按上述流程同步到移动端测试
3. **频繁修改**：如果需要频繁测试移动端效果，可以考虑使用 Live Reload 功能