PODS:
  - Capacitor (7.4.2):
    - Capacitor<PERSON><PERSON>ova
  - CapacitorApp (7.0.1):
    - Capacitor
  - Capacitor<PERSON>ordova (7.4.2)
  - CapacitorHaptics (7.0.1):
    - Capacitor
  - CapacitorSplashScreen (7.0.1):
    - Capacitor
  - CapacitorStatusBar (7.0.1):
    - Capacitor
  - CapacitorToast (7.0.1):
    - Capacitor

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../node_modules/@capacitor/app`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorHaptics (from `../../node_modules/@capacitor/haptics`)"
  - "CapacitorSplashScreen (from `../../node_modules/@capacitor/splash-screen`)"
  - "CapacitorStatusBar (from `../../node_modules/@capacitor/status-bar`)"
  - "CapacitorToast (from `../../node_modules/@capacitor/toast`)"

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../node_modules/@capacitor/app"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorHaptics:
    :path: "../../node_modules/@capacitor/haptics"
  CapacitorSplashScreen:
    :path: "../../node_modules/@capacitor/splash-screen"
  CapacitorStatusBar:
    :path: "../../node_modules/@capacitor/status-bar"
  CapacitorToast:
    :path: "../../node_modules/@capacitor/toast"

SPEC CHECKSUMS:
  Capacitor: 9d9e481b79ffaeacaf7a85d6a11adec32bd33b59
  CapacitorApp: febecbb9582cb353aed037e18ec765141f880fe9
  CapacitorCordova: 5e58d04631bc5094894ac106e2bf1da18a9e6151
  CapacitorHaptics: 1f1e17041f435d8ead9ff2a34edd592c6aa6a8d6
  CapacitorSplashScreen: 1d67815a422a9b61539c94f283c08ed56667c0fc
  CapacitorStatusBar: 6e7af040d8fc4dd655999819625cae9c2d74c36f
  CapacitorToast: ddfc4b36080dbe3634c9faf510b20f6ddcff9330

PODFILE CHECKSUM: cdc1592657abd7a8c80903dd1b1a8304e8fd860f

COCOAPODS: 1.16.2
